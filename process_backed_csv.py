#!/usr/bin/env python3
"""
处理 source/backed.csv 文件中的产品名称
生成英文SEO关键词种子词
"""

import sys
import csv
import json
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from utils.data_structures import ProductData, create_product_data
from modules.product_decomposer import decompose_products
from utils.data_loader import load_products_from_file


def load_backed_csv(file_path: str = "source/backed.csv"):
    """
    加载 backed.csv 文件
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        List[ProductData]: 产品数据列表
    """
    
    print(f"[BACKED_CSV] Loading file: {file_path}")
    
    if not Path(file_path).exists():
        print(f"❌ File not found: {file_path}")
        return []
    
    products = []
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            # 使用csv.DictReader处理CSV
            reader = csv.DictReader(f)
            
            for i, row in enumerate(reader, 1):
                # 获取产品统一名称
                product_name = row.get('product_unified_name', '').strip()
                
                if not product_name:
                    print(f"[BACKED_CSV] Warning: Empty product name at row {i}")
                    continue
                
                # 获取产品类别
                product_category = row.get('product_category', '').strip()
                if not product_category:
                    product_category = row.get('product_category_a', '').strip()
                
                # 创建产品对象
                product = create_product_data(
                    name=product_name,
                    category=product_category if product_category else None,
                    description=None  # backed.csv中没有简单的描述字段
                )
                
                # 添加额外信息
                product.source_file = file_path
                product.row_index = i
                
                products.append(product)
                
                # 显示进度
                if i % 50 == 0:
                    print(f"[BACKED_CSV] Loaded {i} products...")
    
    except Exception as e:
        print(f"❌ Error loading CSV file: {e}")
        return []
    
    print(f"✅ Successfully loaded {len(products)} products from {file_path}")
    return products


def save_results_to_csv(decomposition_result, output_file: str = "results/backed_seed_words.csv", append_mode: bool = False):
    """
    将拆解结果保存为CSV文件

    Args:
        decomposition_result: 拆解结果
        output_file: 输出文件路径
        append_mode: 是否追加模式
    """

    # 确保输出目录存在
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)

    mode = 'a' if append_mode else 'w'
    print(f"[RESULTS] Saving results to: {output_file} (mode: {mode})")

    try:
        with open(output_file, mode, newline='', encoding='utf-8') as f:
            writer = csv.writer(f)

            # 如果不是追加模式，写入表头
            if not append_mode:
                headers = [
                    'source_product',
                    'seed_word',
                    'confidence',
                    'importance',
                    'seo_category',
                    'search_intent',
                    'extraction_method',
                    'timestamp'
                ]
                writer.writerow(headers)

            # 写入数据
            for seed_word in decomposition_result.seed_words:
                row = [
                    seed_word.source_product,
                    seed_word.seed_word,
                    seed_word.confidence,
                    getattr(seed_word, 'importance', 'medium'),
                    getattr(seed_word, 'seo_category', ''),
                    getattr(seed_word, 'search_intent', ''),
                    seed_word.extraction_method,
                    seed_word.timestamp
                ]
                writer.writerow(row)

        print(f"✅ Results saved successfully: {output_file}")

    except Exception as e:
        print(f"❌ Error saving results: {e}")


def save_results_to_json(decomposition_result, output_file: str = "results/backed_seed_words.json"):
    """
    将拆解结果保存为JSON文件
    
    Args:
        decomposition_result: 拆解结果
        output_file: 输出文件路径
    """
    
    # 确保输出目录存在
    output_path = Path(output_file)
    output_path.parent.mkdir(parents=True, exist_ok=True)
    
    print(f"[RESULTS] Saving JSON results to: {output_file}")
    
    try:
        # 按产品分组
        products_data = {}
        
        for seed_word in decomposition_result.seed_words:
            product_name = seed_word.source_product
            
            if product_name not in products_data:
                products_data[product_name] = {
                    "product_name": product_name,
                    "seed_words": [],
                    "total_seed_words": 0
                }
            
            seed_word_data = {
                "word": seed_word.seed_word,
                "confidence": seed_word.confidence,
                "importance": getattr(seed_word, 'importance', 'medium'),
                "seo_category": getattr(seed_word, 'seo_category', ''),
                "search_intent": getattr(seed_word, 'search_intent', ''),
                "extraction_method": seed_word.extraction_method,
                "timestamp": seed_word.timestamp
            }
            
            products_data[product_name]["seed_words"].append(seed_word_data)
            products_data[product_name]["total_seed_words"] += 1
        
        # 创建最终结果
        final_result = {
            "metadata": {
                "total_products": len(products_data),
                "total_seed_words": len(decomposition_result.seed_words),
                "processing_stats": decomposition_result.processing_stats,
                "generation_time": datetime.now().isoformat(),
                "success": decomposition_result.success
            },
            "products": list(products_data.values())
        }
        
        # 保存JSON文件
        with open(output_file, 'w', encoding='utf-8') as f:
            json.dump(final_result, f, indent=2, ensure_ascii=False)
        
        print(f"✅ JSON results saved successfully: {output_file}")
        
    except Exception as e:
        print(f"❌ Error saving JSON results: {e}")


def process_batch(products, batch_size: int = 20, start_index: int = 0):
    """
    批量处理产品
    
    Args:
        products: 产品列表
        batch_size: 批次大小
        start_index: 开始索引
        
    Returns:
        DecompositionResult: 拆解结果
    """
    
    total_products = len(products)
    end_index = min(start_index + batch_size, total_products)
    
    print(f"\n[BATCH] Processing products {start_index + 1} to {end_index} of {total_products}")
    
    batch_products = products[start_index:end_index]
    
    # 显示当前批次的产品
    print(f"[BATCH] Current batch products:")
    for i, product in enumerate(batch_products, start_index + 1):
        print(f"  {i}. {product.name}")
    
    # 执行拆解
    print(f"\n[BATCH] Starting decomposition...")
    result = decompose_products(batch_products)
    
    return result


def main():
    """主函数"""
    
    print("🔬 Keyword Architect Agent - Processing backed.csv")
    print("=" * 60)
    
    # 检查命令行参数
    batch_size = 20
    start_index = 0
    
    if len(sys.argv) > 1:
        try:
            batch_size = int(sys.argv[1])
        except ValueError:
            print(f"Invalid batch size: {sys.argv[1]}, using default: {batch_size}")
    
    if len(sys.argv) > 2:
        try:
            start_index = int(sys.argv[2])
        except ValueError:
            print(f"Invalid start index: {sys.argv[2]}, using default: {start_index}")
    
    print(f"Batch size: {batch_size}")
    print(f"Start index: {start_index}")
    print()
    
    # 加载产品数据
    products = load_backed_csv()
    
    if not products:
        print("❌ No products loaded. Exiting.")
        return
    
    print(f"📊 Total products available: {len(products)}")
    
    # 处理批次
    result = process_batch(products, batch_size, start_index)
    
    # 显示结果
    print("\n" + "=" * 60)
    print("Processing Results")
    print("=" * 60)
    
    if result.success:
        print(f"✅ Processing successful!")
        print(f"Total seed words generated: {len(result.seed_words)}")
        
        # 按产品分组显示结果
        product_groups = {}
        for seed_word in result.seed_words:
            product_name = seed_word.source_product
            if product_name not in product_groups:
                product_groups[product_name] = []
            product_groups[product_name].append(seed_word)
        
        print(f"\n📦 Results by product:")
        for product_name, words in product_groups.items():
            print(f"\n• {product_name}")
            print(f"  Seed words ({len(words)}): {', '.join([w.seed_word for w in words[:5]])}")
            if len(words) > 5:
                print(f"  ... and {len(words) - 5} more")
        
        # 显示统计信息
        if result.processing_stats:
            print(f"\n📊 Processing Statistics:")
            stats = result.processing_stats
            print(f"   Total products: {stats.get('total_products', 0)}")
            print(f"   Successful: {stats.get('successful_decompositions', 0)}")
            print(f"   Failed: {stats.get('failed_decompositions', 0)}")
            print(f"   Success rate: {stats.get('success_rate', 0):.1%}")
            print(f"   Avg seed words/product: {stats.get('avg_seed_words_per_product', 0):.1f}")
        
        # 保存结果
        print(f"\n💾 Saving results...")
        save_results_to_csv(result)
        save_results_to_json(result)
        
    else:
        print(f"❌ Processing failed!")
        if result.errors:
            print("Errors:")
            for error in result.errors:
                print(f"   • {error}")
    
    if result.warnings:
        print(f"\n⚠️  Warnings:")
        for warning in result.warnings:
            print(f"   • {warning}")
    
    print("\n" + "=" * 60)
    print("🎉 Processing complete!")
    print("=" * 60)


if __name__ == "__main__":
    main()
