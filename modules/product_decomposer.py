"""
产品拆解模块 - 将产品名称拆解为原子化关键词
支持Gemini API轮换，不使用try-except（除API调用外）
"""

import re
import json
from typing import List, Tuple, Dict, Any
from datetime import datetime

from config.gemini_api_manager import AutoGenAgentWithRotation
from config.settings import get_default_gemini_config
from utils.data_structures import ProductData, SeedWord, DecompositionResult, validate_product_name, validate_seed_word
from utils.prompt_manager import get_product_decomposer_system_message, get_decomposition_prompt


class ProductDecomposer:
    """产品拆解器（支持Gemini API轮换）"""
    
    def __init__(self):
        """初始化产品拆解器"""
        self.config = get_default_gemini_config()
        self.autogen_agent = AutoGenAgentWithRotation(self.config)
        self.decomposition_stats = {
            "total_products": 0,
            "successful_decompositions": 0,
            "failed_decompositions": 0,
            "total_seed_words": 0
        }

        # 设置专门的system message
        self.system_message = get_product_decomposer_system_message()

        print("[DECOMPOSER] 产品拆解器初始化完成")
    
    def decompose_products(self, products: List[ProductData]) -> DecompositionResult:
        """
        拆解产品列表
        
        Args:
            products: 产品数据列表
            
        Returns:
            DecompositionResult: 拆解结果
        """
        print(f"[DECOMPOSER] 开始拆解{len(products)}个产品")
        
        result = DecompositionResult(success=True)
        
        # 验证输入
        if not products:
            result.success = False
            result.errors.append("产品列表为空")
            return result
        
        # 初始化AutoGen Agent（使用专门的system message）
        if not self.autogen_agent.agent:
            init_success, init_message = self.autogen_agent.initialize_agent(self.system_message)
            if not init_success:
                result.success = False
                result.errors.append(f"AutoGen初始化失败: {init_message}")
                return result
        
        # 逐个处理产品
        for i, product in enumerate(products):
            print(f"[DECOMPOSER] 处理产品 {i+1}/{len(products)}: {product.name}")
            
            # 验证产品名称
            name_valid, name_errors = validate_product_name(product.name)
            if not name_valid:
                result.warnings.extend([f"产品 {product.name} 验证失败: {err}" for err in name_errors])
                continue
            
            # 拆解单个产品
            decomp_success, seed_words, decomp_errors = self._decompose_single_product(product)
            
            if decomp_success:
                result.seed_words.extend(seed_words)
                self.decomposition_stats["successful_decompositions"] += 1
                self.decomposition_stats["total_seed_words"] += len(seed_words)
                print(f"[DECOMPOSER] 产品 {product.name} 拆解成功，获得{len(seed_words)}个种子词")
            else:
                result.errors.extend(decomp_errors)
                self.decomposition_stats["failed_decompositions"] += 1
                print(f"[DECOMPOSER] 产品 {product.name} 拆解失败")
            
            self.decomposition_stats["total_products"] += 1
        
        # 生成处理统计
        result.processing_stats = self._generate_processing_stats()
        
        # 判断整体成功状态
        if self.decomposition_stats["successful_decompositions"] == 0:
            result.success = False
            result.errors.append("没有成功拆解任何产品")
        
        print(f"[DECOMPOSER] 拆解完成，成功: {self.decomposition_stats['successful_decompositions']}, "
              f"失败: {self.decomposition_stats['failed_decompositions']}, "
              f"总种子词: {self.decomposition_stats['total_seed_words']}")
        
        return result
    
    def _decompose_single_product(self, product: ProductData) -> Tuple[bool, List[SeedWord], List[str]]:
        """
        拆解单个产品
        
        Args:
            product: 产品数据
            
        Returns:
            Tuple[bool, List[SeedWord], List[str]]: (成功状态, 种子词列表, 错误信息)
        """
        
        # 创建拆解提示词
        prompt = self._create_decomposition_prompt(product)
        
        # 调用LLM进行拆解
        call_success, response, error_msg = self.autogen_agent.call_with_rotation(prompt)
        
        if not call_success:
            return False, [], [f"LLM调用失败: {error_msg}"]
        
        # 解析LLM响应
        parse_success, seed_words, parse_errors = self._parse_decomposition_response(response, product)
        
        if not parse_success:
            return False, [], parse_errors
        
        # 验证和清理种子词
        validated_seed_words = self._validate_and_clean_seed_words(seed_words)
        
        if not validated_seed_words:
            return False, [], ["没有获得有效的种子词"]
        
        return True, validated_seed_words, []
    
    def _create_decomposition_prompt(self, product: ProductData) -> str:
        """
        创建产品拆解提示词（使用prompt_manager）

        Args:
            product: 产品数据

        Returns:
            str: 拆解提示词
        """

        return get_decomposition_prompt(
            product_name=product.name,
            product_category=product.category,
            product_description=product.description
        )
    
    def _parse_decomposition_response(self, response: str, product: ProductData) -> Tuple[bool, List[SeedWord], List[str]]:
        """
        解析LLM拆解响应
        
        Args:
            response: LLM响应文本
            product: 产品数据
            
        Returns:
            Tuple[bool, List[SeedWord], List[str]]: (成功状态, 种子词列表, 错误信息)
        """
        
        errors = []
        seed_words = []
        
        # 清理响应文本
        cleaned_response = self._clean_response_text(response)
        
        # 添加调试信息
        print(f"[DEBUG] LLM响应: {cleaned_response[:200]}...")

        # 尝试解析JSON
        json_success, json_data, json_error = self._extract_json_from_response(cleaned_response)

        if not json_success:
            print(f"[DEBUG] JSON解析失败: {json_error}")
            errors.append(f"JSON解析失败: {json_error}")
            # 尝试备用解析方法
            fallback_success, fallback_words = self._fallback_parse_response(cleaned_response, product)
            if fallback_success:
                print(f"[DEBUG] 备用解析成功，获得{len(fallback_words)}个种子词")
                return True, fallback_words, []
            else:
                return False, [], errors
        
        # 验证JSON结构
        if "seed_words" not in json_data:
            errors.append("响应中缺少seed_words字段")
            return False, [], errors
        
        if not isinstance(json_data["seed_words"], list):
            errors.append("seed_words必须是列表")
            return False, [], errors
        
        # 解析种子词
        for item in json_data["seed_words"]:
            if not isinstance(item, dict):
                continue
            
            word = item.get("word", "").strip()
            confidence = item.get("confidence", 0.8)
            importance = item.get("importance", "medium")
            
            if not word:
                continue
            
            # 验证种子词
            word_valid, word_errors = validate_seed_word(word)
            if not word_valid:
                errors.extend([f"种子词 {word} 验证失败: {err}" for err in word_errors])
                continue
            
            # 获取额外字段
            importance = item.get("importance", "medium")
            seo_category = item.get("seo_category", "")
            search_intent = item.get("search_intent", "")

            # 创建种子词对象
            seed_word = SeedWord(
                seed_word=word,
                source_product=product.name,
                confidence=float(confidence) if isinstance(confidence, (int, float)) else 0.8,
                extraction_method="llm_decomposition",
                timestamp=datetime.now().isoformat()
            )

            # 添加额外属性
            seed_word.importance = importance
            seed_word.seo_category = seo_category
            seed_word.search_intent = search_intent
            
            seed_words.append(seed_word)
        
        if not seed_words:
            errors.append("没有解析到有效的种子词")
            return False, [], errors
        
        return True, seed_words, errors
    
    def _clean_response_text(self, response: str) -> str:
        """清理响应文本"""
        if not response:
            return ""
        
        # 移除markdown代码块标记
        response = re.sub(r'```json\s*', '', response)
        response = re.sub(r'```\s*', '', response)
        
        # 移除多余的空白字符
        response = response.strip()
        
        return response
    
    def _extract_json_from_response(self, response: str) -> Tuple[bool, Dict[str, Any], str]:
        """从响应中提取JSON（允许使用try-except）"""

        # 查找JSON开始和结束位置
        json_start = response.find('{')
        json_end = response.rfind('}')

        if json_start == -1 or json_end == -1 or json_start >= json_end:
            return False, {}, "未找到有效的JSON结构"

        json_str = response[json_start:json_end + 1]

        # 使用json.loads解析（允许使用try-except处理JSON解析错误）
        try:
            parsed_data = json.loads(json_str)
            return True, parsed_data, ""
        except json.JSONDecodeError as e:
            return False, {}, f"JSON解析错误: {str(e)}"
        except Exception as e:
            return False, {}, f"JSON解析异常: {str(e)}"
    
    def _fallback_parse_response(self, response: str, product: ProductData) -> Tuple[bool, List[SeedWord]]:
        """备用解析方法（当JSON解析失败时）"""

        seed_words = []

        # 使用正则表达式提取可能的关键词
        # 查找引号中的词汇
        quoted_words = re.findall(r'"([^"]+)"', response)

        # 查找列表项
        list_items = re.findall(r'[-*]\s*([^\n]+)', response)

        # 合并所有候选词
        candidates = quoted_words + list_items

        # 过滤掉JSON结构相关的词汇
        json_keywords = {'seed_words', 'word', 'confidence', 'importance', 'high', 'medium', 'low'}

        for candidate in candidates:
            word = candidate.strip()

            # 跳过JSON结构关键词
            if word.lower() in json_keywords:
                continue

            # 基本验证
            if len(word) < 2 or len(word) > 20:
                continue

            # 检查是否是有意义的词汇
            if self._is_meaningful_word(word):
                seed_word = SeedWord(
                    seed_word=word,
                    source_product=product.name,
                    confidence=0.6,  # 备用解析的置信度较低
                    extraction_method="fallback_parsing",
                    timestamp=datetime.now().isoformat()
                )
                seed_words.append(seed_word)

        return len(seed_words) > 0, seed_words[:10]  # 最多返回10个
    
    def _is_meaningful_word(self, word: str) -> bool:
        """判断是否是有意义的词汇"""
        
        # 过滤掉常见的无意义词汇
        stop_words = {
            'the', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 'of', 'with', 'by',
            'a', 'an', 'is', 'are', 'was', 'were', 'be', 'been', 'being', 'have', 'has', 'had',
            'do', 'does', 'did', 'will', 'would', 'could', 'should', 'may', 'might', 'must',
            'can', 'this', 'that', 'these', 'those', 'i', 'you', 'he', 'she', 'it', 'we', 'they'
        }
        
        if word.lower() in stop_words:
            return False
        
        # 检查是否包含字母
        if not re.search(r'[a-zA-Z]', word):
            return False
        
        # 检查是否主要由特殊字符组成
        if len(re.sub(r'[a-zA-Z0-9\s-]', '', word)) > len(word) * 0.3:
            return False
        
        return True
    
    def _validate_and_clean_seed_words(self, seed_words: List[SeedWord]) -> List[SeedWord]:
        """验证和清理种子词列表"""
        
        validated_words = []
        seen_words = set()
        
        for seed_word in seed_words:
            # 去重
            word_lower = seed_word.seed_word.lower()
            if word_lower in seen_words:
                continue
            
            # 验证种子词
            word_valid, _ = validate_seed_word(seed_word.seed_word)
            if not word_valid:
                continue
            
            seen_words.add(word_lower)
            validated_words.append(seed_word)
        
        # 按置信度排序
        validated_words.sort(key=lambda x: x.confidence, reverse=True)
        
        return validated_words
    
    def _generate_processing_stats(self) -> Dict[str, Any]:
        """生成处理统计信息"""
        
        stats = self.decomposition_stats.copy()
        
        if stats["total_products"] > 0:
            stats["success_rate"] = stats["successful_decompositions"] / stats["total_products"]
        else:
            stats["success_rate"] = 0.0
        
        if stats["successful_decompositions"] > 0:
            stats["avg_seed_words_per_product"] = stats["total_seed_words"] / stats["successful_decompositions"]
        else:
            stats["avg_seed_words_per_product"] = 0.0
        
        stats["timestamp"] = datetime.now().isoformat()
        
        return stats
    
    def get_api_stats(self) -> Dict[str, Any]:
        """获取API使用统计"""
        return self.autogen_agent.api_manager.get_api_stats()


def decompose_products(products: List[ProductData]) -> DecompositionResult:
    """
    拆解产品列表（主要函数）
    
    Args:
        products: 产品数据列表
        
    Returns:
        DecompositionResult: 拆解结果
    """
    decomposer = ProductDecomposer()
    return decomposer.decompose_products(products)
