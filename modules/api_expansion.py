#!/usr/bin/env python3
"""
API Expansion Module - API驱动扩展模块
使用Google Keywords Planner API扩展关键词，严格管理10次/小时限制
"""

import time
import yaml
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

# Google Ads API
try:
    from google.ads.googleads.client import GoogleAdsClient
    from google.ads.googleads.errors import GoogleAdsException
except ImportError as e:
    print(f"[ERROR] Google Ads API 导入失败: {e}")
    print("请安装: pip install google-ads")
    raise

from .api_limiter import api_limiter, check_api_quota, record_api_call
from .unified_storage import save_to_unified_storage


@dataclass
class ExpandedKeyword:
    """扩展关键词数据结构"""
    seed_word: str
    keyword: str
    search_volume: Optional[int]
    competition: Optional[str]
    cpc: Optional[float]
    source: str
    timestamp: str


class GoogleKeywordsAPIClient:
    """
    Google Keywords Planner API客户端
    
    核心功能:
    - 严格管理API调用限制
    - 缓存优先策略
    - 详细记录调用历史
    """
    
    def __init__(self):
        """初始化Google Ads API客户端"""
        self.client = None
        self.customer_id = None
        self.cache_dir = Path("cache")
        self.cache_dir.mkdir(exist_ok=True)
        
        # 初始化API客户端
        self._initialize_client()
    
    def _initialize_client(self) -> None:
        """初始化Google Ads客户端"""
        try:
            # 加载配置
            config_path = Path("google-ads.yaml")
            if not config_path.exists():
                raise FileNotFoundError("google-ads.yaml 配置文件不存在")
            
            # 创建客户端
            self.client = GoogleAdsClient.load_from_storage(str(config_path))
            
            # 从配置文件读取customer_id
            with open(config_path, 'r') as f:
                config = yaml.safe_load(f)
                self.customer_id = config.get('login_customer_id') or config.get('customer_id')
            
            if not self.customer_id:
                raise ValueError("配置文件中未找到customer_id")
            
            print(f"[API_EXPANSION] Google Ads API 客户端初始化完成")
            print(f"[API_EXPANSION] Customer ID: {self.customer_id}")
            
        except Exception as e:
            print(f"[ERROR] Google Ads API 初始化失败: {e}")
            raise
    
    def _get_cache_key(self, seed_word: str) -> str:
        """生成缓存键"""
        return f"expansion_{seed_word.lower().replace(' ', '_')}.json"
    
    def _load_from_cache(self, seed_word: str) -> Optional[List[ExpandedKeyword]]:
        """从缓存加载扩展结果"""
        cache_file = self.cache_dir / self._get_cache_key(seed_word)
        
        if cache_file.exists():
            try:
                import json
                with open(cache_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                # 检查缓存是否过期（24小时）
                cache_time = datetime.fromisoformat(data.get('timestamp', ''))
                if (datetime.now() - cache_time).total_seconds() < 86400:  # 24小时
                    keywords = [ExpandedKeyword(**kw) for kw in data['keywords']]
                    print(f"[API_EXPANSION] 从缓存加载: {seed_word} ({len(keywords)} 个关键词)")
                    return keywords
                    
            except Exception as e:
                print(f"[WARNING] 缓存加载失败: {e}")
        
        return None
    
    def _save_to_cache(self, seed_word: str, keywords: List[ExpandedKeyword]) -> None:
        """保存结果到缓存"""
        cache_file = self.cache_dir / self._get_cache_key(seed_word)
        
        try:
            import json
            cache_data = {
                'timestamp': datetime.now().isoformat(),
                'seed_word': seed_word,
                'keywords': [asdict(kw) for kw in keywords]
            }
            
            with open(cache_file, 'w', encoding='utf-8') as f:
                json.dump(cache_data, f, indent=2, ensure_ascii=False)
                
            print(f"[API_EXPANSION] 缓存保存: {seed_word}")
            
        except Exception as e:
            print(f"[WARNING] 缓存保存失败: {e}")
    
    def call_keywords_planner_api(self, seed_word: str) -> List[ExpandedKeyword]:
        """
        调用Google Keywords Planner API
        
        Args:
            seed_word: 种子词
            
        Returns:
            List[ExpandedKeyword]: 扩展关键词列表
        """
        start_time = time.time()
        
        try:
            # 构建请求
            keyword_plan_idea_service = self.client.get_service("KeywordPlanIdeaService")
            
            request = self.client.get_type("GenerateKeywordIdeasRequest")
            request.customer_id = self.customer_id
            
            # 设置种子词
            request.keyword_seed.keywords.append(seed_word)
            
            # 设置地理位置（美国）
            request.geo_target_constants.append("geoTargetConstants/2840")  # US

            # 设置语言（英语）
            request.language = "languageConstants/1000"  # English
            
            # 设置关键词计划网络
            request.keyword_plan_network = self.client.enums.KeywordPlanNetworkEnum.GOOGLE_SEARCH
            
            # 调用API
            print(f"[API_EXPANSION] 调用API: {seed_word}")
            response = keyword_plan_idea_service.generate_keyword_ideas(request=request)
            
            # 处理响应
            keywords = []
            for idea in response.results:
                # 安全地获取搜索量
                search_volume = None
                competition = None
                cpc = None

                if hasattr(idea, 'keyword_idea_metrics') and idea.keyword_idea_metrics:
                    metrics = idea.keyword_idea_metrics

                    # 获取搜索量
                    if hasattr(metrics, 'avg_monthly_searches'):
                        search_volume = metrics.avg_monthly_searches

                    # 获取竞争度
                    if hasattr(metrics, 'competition'):
                        competition = str(metrics.competition)

                    # 获取CPC - 修复字段名称问题
                    if hasattr(metrics, 'low_top_of_page_bid_micros'):
                        cpc = float(metrics.low_top_of_page_bid_micros) / 1000000
                    elif hasattr(metrics, 'high_top_of_page_bid_micros'):
                        cpc = float(metrics.high_top_of_page_bid_micros) / 1000000

                keyword = ExpandedKeyword(
                    seed_word=seed_word,
                    keyword=idea.text,
                    search_volume=search_volume,
                    competition=competition,
                    cpc=cpc,
                    source="google_keyword_planner",
                    timestamp=datetime.now().isoformat()
                )
                keywords.append(keyword)
            
            response_time = time.time() - start_time
            
            # 记录成功的API调用
            record_api_call(seed_word, True, len(keywords), None, response_time)
            
            print(f"[API_EXPANSION] API调用成功: {seed_word} -> {len(keywords)} 个关键词")
            
            return keywords
            
        except GoogleAdsException as ex:
            response_time = time.time() - start_time
            error_msg = f"Google Ads API错误: {ex.error.message if ex.error else str(ex)}"
            
            # 记录失败的API调用
            record_api_call(seed_word, False, 0, error_msg, response_time)
            
            print(f"[ERROR] {error_msg}")
            raise
            
        except Exception as e:
            response_time = time.time() - start_time
            error_msg = f"API调用异常: {str(e)}"
            
            # 记录失败的API调用
            record_api_call(seed_word, False, 0, error_msg, response_time)
            
            print(f"[ERROR] {error_msg}")
            raise
    
    def expand_single_seed_word(self, seed_word: str) -> List[ExpandedKeyword]:
        """
        扩展单个种子词（包含缓存检查和API限制）
        
        Args:
            seed_word: 种子词
            
        Returns:
            List[ExpandedKeyword]: 扩展关键词列表
        """
        # 1. 首先检查缓存
        cached_keywords = self._load_from_cache(seed_word)
        if cached_keywords:
            return cached_keywords
        
        # 2. 检查API配额
        if not check_api_quota():
            quota_status = api_limiter.check_quota_status()
            print(f"[API_EXPANSION] API配额已用完，下次可用时间: {quota_status.next_available_time}")
            
            # 返回空结果或使用降级策略
            return []
        
        # 3. 调用API
        try:
            keywords = self.call_keywords_planner_api(seed_word)
            
            # 4. 保存到缓存
            self._save_to_cache(seed_word, keywords)
            
            return keywords
            
        except Exception as e:
            print(f"[ERROR] 种子词 {seed_word} 扩展失败: {e}")
            return []


def expand_keywords_via_api(seed_words: List[str]) -> List[ExpandedKeyword]:
    """
    使用Google Keywords Planner API扩展关键词（主要函数）
    
    Args:
        seed_words: 种子词列表
        
    Returns:
        List[ExpandedKeyword]: 所有扩展关键词列表
    """
    print(f"[API_EXPANSION] 开始扩展 {len(seed_words)} 个种子词...")
    
    # 初始化API客户端
    try:
        api_client = GoogleKeywordsAPIClient()
    except Exception as e:
        print(f"[ERROR] API客户端初始化失败: {e}")
        return []
    
    all_expanded_keywords = []
    
    # 检查初始配额状态
    quota_status = api_limiter.check_quota_status()
    print(f"[API_EXPANSION] 当前配额状态: 已用 {quota_status.used_quota}/{api_limiter.max_calls_per_hour}")
    
    for i, seed_word in enumerate(seed_words, 1):
        print(f"[API_EXPANSION] 处理进度: {i}/{len(seed_words)} - {seed_word}")
        
        try:
            # 检查是否还能调用API
            if not check_api_quota():
                print(f"[API_EXPANSION] API配额已用完，剩余 {len(seed_words) - i + 1} 个种子词将使用缓存")
                break
            
            expanded_keywords = api_client.expand_single_seed_word(seed_word)
            all_expanded_keywords.extend(expanded_keywords)
            
            # 添加延迟避免过快调用
            if expanded_keywords:  # 只有实际调用了API才延迟
                time.sleep(1)
            
        except Exception as e:
            print(f"[ERROR] 种子词 {seed_word} 处理失败: {e}")
            continue
    
    # 保存扩展结果
    expansion_data = [asdict(kw) for kw in all_expanded_keywords]
    save_to_unified_storage(expansion_data, "json", "api_expansion_results.json")
    
    # 生成使用报告
    usage_report = api_limiter.generate_usage_report()
    save_to_unified_storage(usage_report, "json", "api_usage_report.json")
    
    print(f"[API_EXPANSION] 扩展完成，总计获得 {len(all_expanded_keywords)} 个关键词")
    print(f"[API_EXPANSION] API调用统计: {usage_report['last_24h_stats']}")
    
    return all_expanded_keywords


def get_seed_words_from_decomposition() -> List[str]:
    """
    从拆解结果中提取种子词列表
    
    Returns:
        List[str]: 种子词列表
    """
    try:
        import json
        
        # 从统一存储加载拆解结果
        decomposition_file = Path("results/decomposition_results.json")
        if not decomposition_file.exists():
            print("[WARNING] 拆解结果文件不存在")
            return []
        
        with open(decomposition_file, 'r', encoding='utf-8') as f:
            decomposition_data = json.load(f)
        
        # 提取种子词，去重
        seed_words = list(set([item['keyword'] for item in decomposition_data if item.get('keyword')]))
        
        print(f"[API_EXPANSION] 从拆解结果中提取到 {len(seed_words)} 个唯一种子词")
        
        return seed_words
        
    except Exception as e:
        print(f"[ERROR] 提取种子词失败: {e}")
        return []
