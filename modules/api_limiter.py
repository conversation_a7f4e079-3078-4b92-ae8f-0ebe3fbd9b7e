#!/usr/bin/env python3
"""
API Call Limiter - Google Keywords Planner API调用限制管理
严格管理10次/小时的API调用限制，记录详细调用历史
"""

import json
import os
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Any
from dataclasses import dataclass, asdict
from pathlib import Path


@dataclass
class APICallRecord:
    """API调用记录"""
    timestamp: str
    seed_word: str
    success: bool
    result_count: int
    api_key_id: str  # 新增：标识使用的API密钥
    error_message: Optional[str] = None
    response_time: Optional[float] = None


@dataclass
class QuotaStatus:
    """API配额状态"""
    can_make_call: bool
    used_quota: int
    remaining_quota: int
    current_api_key_id: str  # 新增：当前使用的API密钥ID
    next_available_time: Optional[str] = None
    current_hour: str = ""


class APICallLimiter:
    """
    Google Keywords Planner API调用限制管理器（支持多API轮换）

    核心功能:
    - 严格控制10次/小时限制
    - 详细记录每次调用历史
    - 智能缓存策略
    - 多API自动轮换
    """

    def __init__(self, max_calls_per_hour: int = 10):
        """
        初始化API调用限制管理器

        Args:
            max_calls_per_hour: 每小时最大调用次数，默认10次
        """
        self.max_calls_per_hour = max_calls_per_hour
        self.history_file = Path("results/api_call_history.json")
        self.cache_dir = Path("cache")

        # 多API配置
        self.api_keys = {
            "api_1": "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E",
            "api_2" : 'AIzaSyBLydBhLMf-_Y4eJ7puw5OAvJD5Uq5Umi0'
        }
        self.current_api_key_id = "api_1"  # 默认使用第一个API

        # 确保目录存在
        self.history_file.parent.mkdir(exist_ok=True)
        self.cache_dir.mkdir(exist_ok=True)

        # 初始化历史记录
        self._ensure_history_file()
    
    def _ensure_history_file(self) -> None:
        """确保历史记录文件存在"""
        if not self.history_file.exists():
            self._save_history([])
    
    def _load_history(self) -> List[APICallRecord]:
        """加载API调用历史记录（兼容旧格式）"""
        try:
            with open(self.history_file, 'r', encoding='utf-8') as f:
                data = json.load(f)

                # 兼容旧格式：为没有api_key_id的记录添加默认值
                records = []
                for record in data:
                    if 'api_key_id' not in record:
                        record['api_key_id'] = 'api_1'  # 默认为第一个API
                    records.append(APICallRecord(**record))

                return records
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    
    def _save_history(self, history: List[APICallRecord]) -> None:
        """保存API调用历史记录"""
        with open(self.history_file, 'w', encoding='utf-8') as f:
            json.dump([asdict(record) for record in history], f, indent=2, ensure_ascii=False)
    
    def _get_current_hour(self) -> datetime:
        """获取当前小时的开始时间"""
        now = datetime.now()
        return now.replace(minute=0, second=0, microsecond=0)
    
    def _count_current_hour_calls(self, history: List[APICallRecord], api_key_id: Optional[str] = None) -> int:
        """
        计算当前小时内的成功调用次数

        Args:
            history: 调用历史记录
            api_key_id: 指定API密钥ID，如果为None则计算所有API的总调用次数
        """
        current_hour = self._get_current_hour()
        count = 0

        for record in history:
            try:
                record_time = datetime.fromisoformat(record.timestamp)
                record_hour = record_time.replace(minute=0, second=0, microsecond=0)

                # 检查时间和成功状态
                if record_hour == current_hour and record.success:
                    # 如果指定了API密钥ID，只计算该API的调用次数
                    if api_key_id is None or record.api_key_id == api_key_id:
                        count += 1
            except ValueError:
                continue

        return count
    
    def get_available_api_key(self) -> Optional[str]:
        """
        获取可用的API密钥ID（支持自动轮换）

        Returns:
            Optional[str]: 可用的API密钥ID，如果都不可用则返回None
        """
        history = self._load_history()

        # 检查所有API密钥的可用性
        for api_key_id in self.api_keys.keys():
            used_quota = self._count_current_hour_calls(history, api_key_id)
            if used_quota < self.max_calls_per_hour:
                return api_key_id

        return None

    def switch_to_next_api(self) -> bool:
        """
        切换到下一个可用的API

        Returns:
            bool: 是否成功切换
        """
        available_api = self.get_available_api_key()
        if available_api and available_api != self.current_api_key_id:
            old_api = self.current_api_key_id
            self.current_api_key_id = available_api
            print(f"[API_LIMITER] API切换: {old_api} -> {self.current_api_key_id}")
            return True
        return False

    def get_current_api_key(self) -> str:
        """
        获取当前使用的API密钥

        Returns:
            str: 当前API密钥
        """
        return self.api_keys[self.current_api_key_id]

    def check_quota_status(self) -> QuotaStatus:
        """
        检查当前API调用配额状态（支持多API）

        Returns:
            QuotaStatus: 配额状态信息
        """
        history = self._load_history()
        current_hour = self._get_current_hour()

        # 检查当前API的配额
        used_quota = self._count_current_hour_calls(history, self.current_api_key_id)
        remaining_quota = self.max_calls_per_hour - used_quota
        can_make_call = remaining_quota > 0

        # 如果当前API不可用，尝试切换
        if not can_make_call:
            if self.switch_to_next_api():
                # 重新检查新API的配额
                used_quota = self._count_current_hour_calls(history, self.current_api_key_id)
                remaining_quota = self.max_calls_per_hour - used_quota
                can_make_call = remaining_quota > 0

        next_available_time = None
        if not can_make_call:
            next_available_time = (current_hour + timedelta(hours=1)).isoformat()

        return QuotaStatus(
            can_make_call=can_make_call,
            used_quota=used_quota,
            remaining_quota=remaining_quota,
            current_api_key_id=self.current_api_key_id,
            next_available_time=next_available_time,
            current_hour=current_hour.isoformat()
        )
    
    def record_api_call(self, seed_word: str, success: bool, result_count: int = 0,
                       error_message: Optional[str] = None, response_time: Optional[float] = None,
                       api_key_id: Optional[str] = None) -> None:
        """
        记录API调用历史（关键功能）

        Args:
            seed_word: 调用的种子词
            success: 调用是否成功
            result_count: 返回的结果数量
            error_message: 错误信息（如果有）
            response_time: 响应时间（秒）
            api_key_id: 使用的API密钥ID，如果为None则使用当前API
        """
        history = self._load_history()

        if api_key_id is None:
            api_key_id = self.current_api_key_id

        record = APICallRecord(
            timestamp=datetime.now().isoformat(),
            seed_word=seed_word,
            success=success,
            result_count=result_count,
            api_key_id=api_key_id,
            error_message=error_message,
            response_time=response_time
        )

        history.append(record)

        # 清理超过24小时的记录
        cutoff_time = datetime.now() - timedelta(hours=24)
        history = [r for r in history if datetime.fromisoformat(r.timestamp) > cutoff_time]

        self._save_history(history)

        print(f"[API_LIMITER] 记录API调用: {seed_word}, API: {api_key_id}, 成功: {success}, 结果数: {result_count}")
    
    def get_call_history(self, hours: int = 24) -> List[APICallRecord]:
        """
        获取指定时间内的API调用历史
        
        Args:
            hours: 查询的小时数
            
        Returns:
            List[APICallRecord]: 调用历史记录
        """
        history = self._load_history()
        cutoff_time = datetime.now() - timedelta(hours=hours)
        
        return [r for r in history if datetime.fromisoformat(r.timestamp) > cutoff_time]
    
    def can_make_api_call(self) -> bool:
        """
        检查是否可以进行API调用
        
        Returns:
            bool: 是否可以调用API
        """
        status = self.check_quota_status()
        return status.can_make_call
    
    def get_next_available_time(self) -> Optional[datetime]:
        """
        获取下次可以调用API的时间
        
        Returns:
            Optional[datetime]: 下次可用时间，如果当前可用则返回None
        """
        status = self.check_quota_status()
        if status.next_available_time:
            return datetime.fromisoformat(status.next_available_time)
        return None
    
    def generate_usage_report(self) -> Dict[str, Any]:
        """
        生成API使用报告（支持多API统计）

        Returns:
            Dict[str, Any]: 使用报告
        """
        history = self.get_call_history(24)
        successful_calls = [r for r in history if r.success]
        failed_calls = [r for r in history if not r.success]

        current_status = self.check_quota_status()

        # 按API分组统计
        api_stats = {}
        for api_key_id in self.api_keys.keys():
            api_calls = [r for r in history if r.api_key_id == api_key_id]
            api_successful = [r for r in api_calls if r.success]
            api_failed = [r for r in api_calls if not r.success]

            api_stats[api_key_id] = {
                "total_calls": len(api_calls),
                "successful_calls": len(api_successful),
                "failed_calls": len(api_failed),
                "current_hour_calls": self._count_current_hour_calls(history, api_key_id),
                "remaining_quota": self.max_calls_per_hour - self._count_current_hour_calls(history, api_key_id)
            }

        return {
            "current_status": asdict(current_status),
            "api_keys_status": api_stats,
            "last_24h_stats": {
                "total_calls": len(history),
                "successful_calls": len(successful_calls),
                "failed_calls": len(failed_calls),
                "total_results": sum(r.result_count for r in successful_calls),
                "average_response_time": sum(r.response_time or 0 for r in successful_calls) / len(successful_calls) if successful_calls else 0
            },
            "recent_calls": [asdict(r) for r in history[-10:]]  # 最近10次调用
        }
    
    def cleanup_old_records(self, days: int = 7) -> int:
        """
        清理旧的API调用记录
        
        Args:
            days: 保留的天数
            
        Returns:
            int: 清理的记录数量
        """
        history = self._load_history()
        cutoff_time = datetime.now() - timedelta(days=days)
        
        old_count = len(history)
        history = [r for r in history if datetime.fromisoformat(r.timestamp) > cutoff_time]
        new_count = len(history)
        
        self._save_history(history)
        
        cleaned_count = old_count - new_count
        if cleaned_count > 0:
            print(f"[API_LIMITER] 清理了 {cleaned_count} 条旧记录")
        
        return cleaned_count


# 全局API限制管理器实例
api_limiter = APICallLimiter()


def check_api_quota() -> bool:
    """检查API配额的便捷函数"""
    return api_limiter.can_make_api_call()


def record_api_call(seed_word: str, success: bool, result_count: int = 0,
                   error_message: Optional[str] = None, response_time: Optional[float] = None,
                   api_key_id: Optional[str] = None) -> None:
    """记录API调用的便捷函数"""
    api_limiter.record_api_call(seed_word, success, result_count, error_message, response_time, api_key_id)


def get_current_api_key() -> str:
    """获取当前可用API密钥的便捷函数"""
    return api_limiter.get_current_api_key()


def get_quota_status() -> QuotaStatus:
    """获取配额状态的便捷函数"""
    return api_limiter.check_quota_status()
