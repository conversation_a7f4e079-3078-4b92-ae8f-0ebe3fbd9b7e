#!/usr/bin/env python3
"""
Unified Storage Management - 统一存储管理模块
统一管理所有结果到results/目录，易于寻找，即时清理无用文件
"""

import json
import os
import pandas as pd
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Any, Optional, Union
from dataclasses import dataclass, asdict
import shutil
import glob


@dataclass
class ProcessingMetadata:
    """处理元数据"""
    start_time: str
    end_time: str
    total_products: int
    total_seed_words: int
    total_expanded_keywords: int
    total_associated_keywords: int
    api_calls_made: int
    processing_duration: float
    success: bool
    error_message: Optional[str] = None


class UnifiedStorageManager:
    """
    统一存储管理器
    
    核心功能:
    - 统一存储所有结果到results/目录
    - 即时清理无用的临时文件
    - 生成易于查找的文件结构
    - 管理处理元数据
    """
    
    def __init__(self):
        """初始化统一存储管理器"""
        self.results_dir = Path("results")
        self.cache_dir = Path("cache")
        self.temp_dir = Path("temp")
        self.docs_dir = Path("docs")
        
        # 确保目录存在
        self._ensure_directories()
        
        # 清理临时文件
        self.cleanup_temp_files()
    
    def _ensure_directories(self) -> None:
        """确保所有必要目录存在"""
        for directory in [self.results_dir, self.cache_dir, self.docs_dir]:
            directory.mkdir(exist_ok=True)
    
    def save_to_unified_storage(self, data: Any, data_type: str, filename: str) -> str:
        """
        保存数据到统一存储位置（results/目录）
        
        Args:
            data: 要保存的数据
            data_type: 数据类型 (csv, json, metadata)
            filename: 文件名
            
        Returns:
            str: 保存的文件路径
        """
        file_path = self.results_dir / filename
        
        try:
            if data_type == "csv":
                if isinstance(data, pd.DataFrame):
                    data.to_csv(file_path, index=False, encoding='utf-8')
                else:
                    raise ValueError("CSV数据必须是pandas DataFrame")
                    
            elif data_type == "json":
                with open(file_path, 'w', encoding='utf-8') as f:
                    if hasattr(data, '__dict__') or isinstance(data, dict):
                        json.dump(data if isinstance(data, dict) else asdict(data), 
                                f, indent=2, ensure_ascii=False, default=str)
                    else:
                        json.dump(data, f, indent=2, ensure_ascii=False, default=str)
                        
            elif data_type == "text":
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(str(data))
                    
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
            
            print(f"[STORAGE] 保存数据到: {file_path}")
            return str(file_path)
            
        except Exception as e:
            print(f"[STORAGE] 保存失败: {e}")
            raise
    
    def load_from_storage(self, filename: str, data_type: str) -> Any:
        """
        从统一存储位置加载数据
        
        Args:
            filename: 文件名
            data_type: 数据类型 (csv, json, text)
            
        Returns:
            Any: 加载的数据
        """
        file_path = self.results_dir / filename
        
        if not file_path.exists():
            raise FileNotFoundError(f"文件不存在: {file_path}")
        
        try:
            if data_type == "csv":
                return pd.read_csv(file_path, encoding='utf-8')
                
            elif data_type == "json":
                with open(file_path, 'r', encoding='utf-8') as f:
                    return json.load(f)
                    
            elif data_type == "text":
                with open(file_path, 'r', encoding='utf-8') as f:
                    return f.read()
                    
            else:
                raise ValueError(f"不支持的数据类型: {data_type}")
                
        except Exception as e:
            print(f"[STORAGE] 加载失败: {e}")
            raise
    
    def consolidate_and_save_final_results(self, decomposed: List[Dict], expanded: List[Dict], 
                                         associated: List[Dict]) -> str:
        """
        整合所有结果并保存为最终表格
        
        Args:
            decomposed: 拆解结果
            expanded: 扩展结果
            associated: 联想结果
            
        Returns:
            str: 最终结果文件路径
        """
        print("[STORAGE] 开始整合最终结果...")
        
        # 创建最终数据表
        final_data = []
        
        # 处理拆解结果
        for item in decomposed:
            final_data.append({
                "keyword": item.get("keyword", ""),
                "type": "seed_word",
                "source": "decomposition",
                "product_id": item.get("product_id", ""),
                "confidence": item.get("confidence", 0.0),
                "search_volume": None,
                "competition": None,
                "cpc": None,
                "scenario": None,
                "timestamp": item.get("timestamp", datetime.now().isoformat())
            })
        
        # 处理扩展结果
        for item in expanded:
            final_data.append({
                "keyword": item.get("keyword", ""),
                "type": "expanded_keyword",
                "source": "api_expansion",
                "product_id": item.get("seed_word", ""),
                "confidence": None,
                "search_volume": item.get("search_volume"),
                "competition": item.get("competition"),
                "cpc": item.get("cpc"),
                "scenario": None,
                "timestamp": item.get("timestamp", datetime.now().isoformat())
            })
        
        # 处理联想结果
        for item in associated:
            final_data.append({
                "keyword": item.get("associated_keyword", ""),
                "type": "associated_keyword",
                "source": "llm_association",
                "product_id": item.get("base_keyword", ""),
                "confidence": item.get("relevance_score"),
                "search_volume": None,
                "competition": None,
                "cpc": None,
                "scenario": item.get("scenario"),
                "timestamp": item.get("timestamp", datetime.now().isoformat())
            })
        
        # 创建DataFrame
        df = pd.DataFrame(final_data)
        
        # 保存最终结果
        final_path = self.save_to_unified_storage(df, "csv", "keyword_data.csv")
        
        print(f"[STORAGE] 最终结果已保存: {final_path}")
        print(f"[STORAGE] 总计关键词数量: {len(final_data)}")
        
        return final_path
    
    def save_processing_metadata(self, metadata: ProcessingMetadata) -> str:
        """
        保存处理元数据
        
        Args:
            metadata: 处理元数据
            
        Returns:
            str: 元数据文件路径
        """
        return self.save_to_unified_storage(metadata, "json", "processing_metadata.json")
    
    def cleanup_temp_files(self) -> int:
        """
        即时清理无用的临时文件和测试文件
        
        Returns:
            int: 清理的文件数量
        """
        cleaned_count = 0
        
        # 清理临时目录
        if self.temp_dir.exists():
            for file_path in self.temp_dir.rglob("*"):
                if file_path.is_file():
                    file_path.unlink()
                    cleaned_count += 1
            
            # 删除空目录
            try:
                shutil.rmtree(self.temp_dir)
            except OSError:
                pass
        
        # 清理测试文件
        test_patterns = [
            "test_*.py",
            "*_test.py",
            "*.tmp",
            "*.temp",
            ".pytest_cache",
            "__pycache__"
        ]
        
        for pattern in test_patterns:
            for file_path in Path(".").rglob(pattern):
                try:
                    if file_path.is_file():
                        file_path.unlink()
                        cleaned_count += 1
                    elif file_path.is_dir():
                        shutil.rmtree(file_path)
                        cleaned_count += 1
                except OSError:
                    continue
        
        if cleaned_count > 0:
            print(f"[STORAGE] 清理了 {cleaned_count} 个临时文件")
        
        return cleaned_count
    
    def organize_results_directory(self) -> None:
        """
        组织results目录结构，确保易于寻找
        """
        print("[STORAGE] 组织results目录结构...")
        
        # 创建子目录
        subdirs = ["archives", "reports", "exports"]
        for subdir in subdirs:
            (self.results_dir / subdir).mkdir(exist_ok=True)
        
        # 移动旧文件到archives
        current_time = datetime.now().strftime("%Y%m%d_%H%M%S")
        
        for file_path in self.results_dir.glob("*.csv"):
            if file_path.name != "keyword_data.csv":  # 保留最新的主文件
                archive_name = f"{file_path.stem}_{current_time}{file_path.suffix}"
                archive_path = self.results_dir / "archives" / archive_name
                file_path.rename(archive_path)
                print(f"[STORAGE] 归档文件: {archive_path}")
    
    def generate_summary_report(self, final_data_path: str) -> str:
        """
        生成处理结果摘要报告
        
        Args:
            final_data_path: 最终数据文件路径
            
        Returns:
            str: 报告文件路径
        """
        try:
            df = pd.read_csv(final_data_path)
            
            report = {
                "generation_time": datetime.now().isoformat(),
                "total_keywords": len(df),
                "by_type": df["type"].value_counts().to_dict(),
                "by_source": df["source"].value_counts().to_dict(),
                "statistics": {
                    "avg_confidence": df["confidence"].mean() if "confidence" in df.columns else None,
                    "total_search_volume": df["search_volume"].sum() if "search_volume" in df.columns else None,
                    "unique_products": df["product_id"].nunique() if "product_id" in df.columns else None
                }
            }
            
            report_path = self.save_to_unified_storage(report, "json", "summary_report.json")
            print(f"[STORAGE] 摘要报告已生成: {report_path}")
            
            return report_path
            
        except Exception as e:
            print(f"[STORAGE] 生成摘要报告失败: {e}")
            return ""
    
    def export_to_multiple_formats(self, data_path: str) -> List[str]:
        """
        导出数据为多种格式
        
        Args:
            data_path: 数据文件路径
            
        Returns:
            List[str]: 导出的文件路径列表
        """
        exported_files = []
        
        try:
            df = pd.read_csv(data_path)
            
            # 导出为Excel
            excel_path = self.results_dir / "exports" / "keyword_data.xlsx"
            df.to_excel(excel_path, index=False)
            exported_files.append(str(excel_path))
            
            # 导出为JSON
            json_path = self.results_dir / "exports" / "keyword_data.json"
            df.to_json(json_path, orient="records", indent=2, force_ascii=False)
            exported_files.append(str(json_path))
            
            print(f"[STORAGE] 导出完成: {len(exported_files)} 个文件")
            
        except Exception as e:
            print(f"[STORAGE] 导出失败: {e}")
        
        return exported_files


# 全局统一存储管理器实例
storage_manager = UnifiedStorageManager()


def save_to_unified_storage(data: Any, data_type: str, filename: str) -> str:
    """保存到统一存储的便捷函数"""
    return storage_manager.save_to_unified_storage(data, data_type, filename)


def cleanup_temp_files() -> int:
    """清理临时文件的便捷函数"""
    return storage_manager.cleanup_temp_files()
