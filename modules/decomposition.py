#!/usr/bin/env python3
"""
Decomposition Module - 原子化拆解模块
使用AutoGen v0.4 + Google Gemini 2.5 Flash + prompt_manager进行产品名称拆解
"""

import pandas as pd
import json
from datetime import datetime
from typing import List, Dict, Any, Optional
from dataclasses import dataclass, asdict
from pathlib import Path

# 必须使用AutoGen v0.4
try:
    from autogen_agentchat.agents import AssistantAgent
    from autogen_agentchat.messages import TextMessage
    from autogen_ext.models.openai import OpenAIChatCompletionClient
except ImportError as e:
    print(f"[ERROR] AutoGen v0.4 导入失败: {e}")
    print("请确保安装了 autogen-agentchat 和 autogen-ext")
    raise

# 必须使用prompt_manager
try:
    from prompt_manager.core import PromptManager
except ImportError as e:
    print(f"[ERROR] prompt_manager 导入失败: {e}")
    print("请确保prompt_manager模块可用")
    raise

from .unified_storage import save_to_unified_storage


@dataclass
class ProductData:
    """产品数据结构"""
    product_idex: str
    product_sku_no: str
    product_unified_name: str
    product_category: str
    product_seo_keywords: Optional[str] = None
    product_seo_description: Optional[str] = None


@dataclass
class SeedWord:
    """种子词数据结构"""
    keyword: str
    type: str  # technical, product, target, application
    confidence: float
    product_id: str
    timestamp: str
    source: str = "decomposition"


class AutoGenDecompositionAgent:
    """
    基于AutoGen v0.4的产品拆解代理
    
    强制要求:
    - 使用AutoGen v0.4框架
    - 使用prompt_manager管理提示词
    """
    
    def __init__(self):
        """初始化AutoGen拆解代理"""
        self.agent = None
        self.prompt_manager = None
        self.prompts = {}

        # 初始化组件
        self._initialize_prompt_manager()
        self._initialize_autogen_agent()
    
    def _initialize_prompt_manager(self) -> None:
        """初始化prompt_manager系统"""
        try:
            self.prompt_manager = PromptManager()

            # 使用prompt_manager创建和管理提示词
            decomposition_prompt_template = """
你是一个专业的生物医学产品关键词分析专家。请将以下产品名称拆解为种子关键词，并按类型分类。

产品名称: {product_name}
产品类别: {product_category}
产品描述: {product_description}

请按以下格式输出，每个类型至少提供2-5个关键词：

TECHNICAL: 科学/医学术语 (如 ELISA, IL-6, PCR)
PRODUCT: 产品类型术语 (如 Kit, Test, Assay, Reagent)
TARGET: 目标对象术语 (如 Human, Mouse, Serum)
APPLICATION: 应用场景术语 (如 Detection, Measurement, Analysis)

输出格式示例:
TECHNICAL: ELISA, IL-6, Interleukin
PRODUCT: Kit, Test, Assay
TARGET: Human, Serum
APPLICATION: Detection, Measurement

请严格按照上述格式输出，不要添加其他内容。
"""

            # 通过prompt_manager注册提示词
            self.prompt_manager.add_prompt(
                name="biomedical_decomposition",
                template=decomposition_prompt_template,
                description="生物医学产品关键词拆解提示词"
            )

            # 系统消息也通过prompt_manager管理
            system_message_template = "你是一个专业的生物医学产品关键词分析专家，专门负责将产品名称拆解为有意义的种子关键词。"

            self.prompt_manager.add_prompt(
                name="decomposition_system_message",
                template=system_message_template,
                description="拆解代理系统消息"
            )

            print("[DECOMPOSITION] prompt_manager 初始化完成")

        except Exception as e:
            print(f"[ERROR] prompt_manager 初始化失败: {e}")
            raise
    
    def _initialize_autogen_agent(self) -> None:
        """初始化AutoGen v0.4代理（使用多API轮换）"""
        try:
            # 从API限制管理器获取当前可用的API密钥
            from .api_limiter import get_current_api_key

            current_api_key = get_current_api_key()

            # 创建模型客户端
            model_client = OpenAIChatCompletionClient(
                model="gemini-2.5-flash",
                api_key=current_api_key,
                base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
            )
            
            # 从prompt_manager获取系统消息
            system_message = self.prompt_manager.get_prompt("decomposition_system_message")

            # 创建AssistantAgent
            self.agent = AssistantAgent(
                name="keyword_decomposer",
                model_client=model_client,
                system_message=system_message
            )
            
            print("[DECOMPOSITION] AutoGen v0.4 代理初始化完成")
            
        except Exception as e:
            print(f"[ERROR] AutoGen 代理初始化失败: {e}")
            raise
    
    def decompose_product_name(self, product: ProductData) -> List[SeedWord]:
        """
        使用AutoGen拆解单个产品名称
        
        Args:
            product: 产品数据
            
        Returns:
            List[SeedWord]: 拆解得到的种子词列表
        """
        try:
            # 从prompt_manager获取提示词模板并格式化
            prompt_template = self.prompt_manager.get_prompt("biomedical_decomposition")
            prompt = prompt_template.format(
                product_name=product.product_unified_name,
                product_category=product.product_category or "生物医学产品",
                product_description=product.product_seo_description or "无描述"
            )
            
            # 使用AutoGen进行拆解
            print(f"[DECOMPOSITION] 正在拆解产品: {product.product_unified_name}")
            
            # 发送消息给代理（同步方式）
            import asyncio

            async def get_response():
                message = TextMessage(content=prompt, source="user")
                response = await self.agent.on_messages([message], cancellation_token=None)
                return response

            # 运行异步函数
            try:
                # 检查是否已经在事件循环中
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 在已有事件循环中，使用线程池
                    import concurrent.futures
                    import threading

                    def run_in_thread():
                        new_loop = asyncio.new_event_loop()
                        asyncio.set_event_loop(new_loop)
                        try:
                            return new_loop.run_until_complete(get_response())
                        finally:
                            new_loop.close()

                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(run_in_thread)
                        response = future.result()
                else:
                    response = asyncio.run(get_response())
            except RuntimeError:
                # 备用方法
                response = asyncio.run(get_response())

            # 解析响应
            if hasattr(response, 'chat_message') and response.chat_message:
                response_text = response.chat_message.content
            else:
                response_text = str(response)
            
            # 解析种子词
            seed_words = self._parse_decomposition_response(response_text, product.product_idex)
            
            print(f"[DECOMPOSITION] 成功拆解，获得 {len(seed_words)} 个种子词")
            return seed_words
            
        except Exception as e:
            print(f"[ERROR] 产品拆解失败: {e}")
            print(f"[ERROR] 产品: {product.product_unified_name}")
            import traceback
            traceback.print_exc()
            # 不要掩盖错误，重新抛出异常
            raise Exception(f"AutoGen拆解失败: {e}") from e
    
    def _parse_decomposition_response(self, response_text: str, product_id: str) -> List[SeedWord]:
        """
        解析AutoGen的拆解响应
        
        Args:
            response_text: AutoGen的响应文本
            product_id: 产品ID
            
        Returns:
            List[SeedWord]: 解析后的种子词列表
        """
        seed_words = []
        current_time = datetime.now().isoformat()
        
        try:
            lines = response_text.strip().split('\n')
            
            for line in lines:
                line = line.strip()
                if ':' in line:
                    parts = line.split(':', 1)
                    if len(parts) == 2:
                        keyword_type = parts[0].strip().lower()
                        keywords_text = parts[1].strip()
                        
                        # 解析关键词
                        keywords = [kw.strip() for kw in keywords_text.split(',') if kw.strip()]
                        
                        for keyword in keywords:
                            if keyword and len(keyword) > 1:
                                confidence = self._calculate_confidence(keyword, keyword_type)
                                
                                seed_word = SeedWord(
                                    keyword=keyword,
                                    type=keyword_type,
                                    confidence=confidence,
                                    product_id=product_id,
                                    timestamp=current_time
                                )
                                seed_words.append(seed_word)
            
        except Exception as e:
            print(f"[WARNING] 解析响应失败: {e}")
            print(f"原始响应: {response_text}")
        
        return seed_words
    
    def _calculate_confidence(self, keyword: str, keyword_type: str) -> float:
        """
        计算种子词的置信度
        
        Args:
            keyword: 关键词
            keyword_type: 关键词类型
            
        Returns:
            float: 置信度分数 (0.0-1.0)
        """
        confidence = 0.5  # 基础分数
        
        # 长度评分
        length = len(keyword)
        if 3 <= length <= 15:
            confidence += 0.2
        elif 16 <= length <= 25:
            confidence += 0.1
        
        # 类型匹配度
        if keyword_type in ["technical", "product"]:
            confidence += 0.2
        elif keyword_type in ["target", "application"]:
            confidence += 0.1
        
        # 生物医学相关性
        biomedical_terms = ["elisa", "pcr", "assay", "kit", "test", "human", "mouse", "serum", "plasma"]
        if any(term in keyword.lower() for term in biomedical_terms):
            confidence += 0.2
        
        return min(1.0, confidence)
    
    def _fallback_decomposition(self, product: ProductData) -> List[SeedWord]:
        """
        降级拆解方法（当AutoGen失败时使用）
        
        Args:
            product: 产品数据
            
        Returns:
            List[SeedWord]: 基础拆解结果
        """
        print("[DECOMPOSITION] 使用降级拆解方法")
        
        seed_words = []
        current_time = datetime.now().isoformat()
        product_name = product.product_unified_name.lower()
        
        # 基础关键词提取
        basic_keywords = {
            "elisa": ("technical", 0.9),
            "kit": ("product", 0.8),
            "test": ("product", 0.8),
            "assay": ("product", 0.9),
            "human": ("target", 0.8),
            "mouse": ("target", 0.8),
            "detection": ("application", 0.7),
            "measurement": ("application", 0.7)
        }
        
        for keyword, (kw_type, confidence) in basic_keywords.items():
            if keyword in product_name:
                seed_word = SeedWord(
                    keyword=keyword.title(),
                    type=kw_type,
                    confidence=confidence,
                    product_id=product.product_idex,
                    timestamp=current_time
                )
                seed_words.append(seed_word)
        
        return seed_words


def load_csv_data(file_path: str) -> List[ProductData]:
    """
    从CSV文件加载产品数据
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        List[ProductData]: 产品数据列表
    """
    try:
        df = pd.read_csv(file_path, encoding='utf-8')
        products = []
        
        for _, row in df.iterrows():
            product = ProductData(
                product_idex=str(row.get('product_idex', '')),
                product_sku_no=str(row.get('product_sku_no', '')),
                product_unified_name=str(row.get('product_unified_name', '')),
                product_category=str(row.get('product_category', '')),
                product_seo_keywords=str(row.get('product_seo_keywords', '')) if pd.notna(row.get('product_seo_keywords')) else None,
                product_seo_description=str(row.get('product_seo_description', '')) if pd.notna(row.get('product_seo_description')) else None
            )
            products.append(product)
        
        print(f"[DECOMPOSITION] 成功加载 {len(products)} 个产品")
        return products
        
    except Exception as e:
        print(f"[ERROR] 加载CSV数据失败: {e}")
        raise


def decompose_product_names(products: List[ProductData]) -> List[SeedWord]:
    """
    批量拆解产品名称（主要函数）
    
    Args:
        products: 产品数据列表
        
    Returns:
        List[SeedWord]: 所有种子词列表
    """
    print(f"[DECOMPOSITION] 开始拆解 {len(products)} 个产品...")
    
    # 初始化AutoGen代理
    agent = AutoGenDecompositionAgent()
    
    all_seed_words = []
    
    for i, product in enumerate(products, 1):
        print(f"[DECOMPOSITION] 处理进度: {i}/{len(products)}")
        
        try:
            seed_words = agent.decompose_product_name(product)
            all_seed_words.extend(seed_words)
            
        except Exception as e:
            print(f"[ERROR] 产品 {product.product_idex} 拆解失败: {e}")
            continue
    
    # 保存拆解结果
    decomposition_data = [asdict(sw) for sw in all_seed_words]
    save_to_unified_storage(decomposition_data, "json", "decomposition_results.json")
    
    print(f"[DECOMPOSITION] 拆解完成，总计获得 {len(all_seed_words)} 个种子词")
    
    return all_seed_words
