#!/usr/bin/env python3
"""
测试产品拆解功能
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from utils.data_structures import ProductData, create_product_data
from modules.product_decomposer import decompose_products
from utils.data_loader import load_products_from_file


def test_with_sample_products():
    """使用示例产品测试拆解功能"""
    
    print("=" * 60)
    print("测试产品拆解功能 - 示例产品")
    print("=" * 60)
    
    # 创建示例产品
    sample_products = [
        create_product_data(
            name="Human Interleukin-6 ELISA Kit",
            category="ELISA Kit",
            description="用于检测人白细胞介素-6的酶联免疫吸附试剂盒"
        ),
        create_product_data(
            name="Anti-CD3 Monoclonal Antibody",
            category="Antibody",
            description="抗CD3单克隆抗体，用于免疫学研究"
        ),
        create_product_data(
            name="PCR Master Mix 2X",
            category="PCR Reagent",
            description="2倍浓缩PCR预混液"
        ),
        create_product_data(
            name="Cell Culture Medium DMEM",
            category="Cell Culture",
            description="杜氏改良Eagle培养基"
        ),
        create_product_data(
            name="Protein Ladder 10-250 kDa",
            category="Protein Standard",
            description="蛋白质分子量标准，范围10-250千道尔顿"
        )
    ]
    
    print(f"准备拆解{len(sample_products)}个示例产品:")
    for i, product in enumerate(sample_products, 1):
        print(f"{i}. {product.name}")
    
    print("\n开始拆解...")
    
    # 执行拆解
    result = decompose_products(sample_products)
    
    # 显示结果
    print("\n" + "=" * 60)
    print("拆解结果")
    print("=" * 60)
    
    if result.success:
        print(f"✅ 拆解成功！")
        print(f"总共获得 {len(result.seed_words)} 个种子词")
        
        # 按产品分组显示结果
        product_groups = {}
        for seed_word in result.seed_words:
            product_name = seed_word.source_product
            if product_name not in product_groups:
                product_groups[product_name] = []
            product_groups[product_name].append(seed_word)
        
        for product_name, words in product_groups.items():
            print(f"\n📦 产品: {product_name}")
            print(f"   种子词数量: {len(words)}")
            for word in words:
                confidence_str = f"{word.confidence:.2f}" if word.confidence else "N/A"
                print(f"   • {word.seed_word} (置信度: {confidence_str})")
        
        # 显示统计信息
        if result.processing_stats:
            print(f"\n📊 处理统计:")
            stats = result.processing_stats
            print(f"   总产品数: {stats.get('total_products', 0)}")
            print(f"   成功拆解: {stats.get('successful_decompositions', 0)}")
            print(f"   失败拆解: {stats.get('failed_decompositions', 0)}")
            print(f"   成功率: {stats.get('success_rate', 0):.1%}")
            print(f"   平均种子词/产品: {stats.get('avg_seed_words_per_product', 0):.1f}")
    
    else:
        print(f"❌ 拆解失败！")
        if result.errors:
            print("错误信息:")
            for error in result.errors:
                print(f"   • {error}")
    
    if result.warnings:
        print(f"\n⚠️  警告信息:")
        for warning in result.warnings:
            print(f"   • {warning}")
    
    return result


def test_with_csv_file(csv_file_path: str):
    """使用CSV文件测试拆解功能"""
    
    print("=" * 60)
    print(f"测试产品拆解功能 - CSV文件: {csv_file_path}")
    print("=" * 60)
    
    # 检查文件是否存在
    if not Path(csv_file_path).exists():
        print(f"❌ 文件不存在: {csv_file_path}")
        return None
    
    # 加载产品数据
    print("正在加载产品数据...")
    load_success, products, load_errors = load_products_from_file(csv_file_path)
    
    if not load_success:
        print(f"❌ 数据加载失败！")
        for error in load_errors:
            print(f"   • {error}")
        return None
    
    print(f"✅ 成功加载 {len(products)} 个产品")
    
    # 显示前几个产品
    print("\n前5个产品:")
    for i, product in enumerate(products[:5], 1):
        print(f"{i}. {product.name}")
        if product.category:
            print(f"   类别: {product.category}")
    
    if len(products) > 5:
        print(f"   ... 还有 {len(products) - 5} 个产品")
    
    # 为了测试，只处理前10个产品
    test_products = products[:10]
    print(f"\n开始拆解前 {len(test_products)} 个产品...")
    
    # 执行拆解
    result = decompose_products(test_products)
    
    # 显示结果（与示例产品测试相同的格式）
    print("\n" + "=" * 60)
    print("拆解结果")
    print("=" * 60)
    
    if result.success:
        print(f"✅ 拆解成功！")
        print(f"总共获得 {len(result.seed_words)} 个种子词")
        
        # 按产品分组显示结果
        product_groups = {}
        for seed_word in result.seed_words:
            product_name = seed_word.source_product
            if product_name not in product_groups:
                product_groups[product_name] = []
            product_groups[product_name].append(seed_word)
        
        for product_name, words in product_groups.items():
            print(f"\n📦 产品: {product_name}")
            print(f"   种子词数量: {len(words)}")
            for word in words:
                confidence_str = f"{word.confidence:.2f}" if word.confidence else "N/A"
                print(f"   • {word.seed_word} (置信度: {confidence_str})")
        
        # 显示统计信息
        if result.processing_stats:
            print(f"\n📊 处理统计:")
            stats = result.processing_stats
            print(f"   总产品数: {stats.get('total_products', 0)}")
            print(f"   成功拆解: {stats.get('successful_decompositions', 0)}")
            print(f"   失败拆解: {stats.get('failed_decompositions', 0)}")
            print(f"   成功率: {stats.get('success_rate', 0):.1%}")
            print(f"   平均种子词/产品: {stats.get('avg_seed_words_per_product', 0):.1f}")
    
    else:
        print(f"❌ 拆解失败！")
        if result.errors:
            print("错误信息:")
            for error in result.errors:
                print(f"   • {error}")
    
    if result.warnings:
        print(f"\n⚠️  警告信息:")
        for warning in result.warnings:
            print(f"   • {warning}")
    
    return result


def main():
    """主函数"""
    
    print("🔬 Keyword Architect Agent - 产品拆解测试")
    print("=" * 60)
    
    # 检查命令行参数
    if len(sys.argv) > 1:
        csv_file_path = sys.argv[1]
        result = test_with_csv_file(csv_file_path)
    else:
        print("未提供CSV文件路径，使用示例产品进行测试")
        print("如需测试CSV文件，请使用: python test_decomposition.py <csv_file_path>")
        print()
        result = test_with_sample_products()
    
    print("\n" + "=" * 60)
    if result and result.success:
        print("🎉 测试完成！产品拆解功能正常工作。")
        print("\n请检查上述结果是否符合预期。")
        print("如果结果满意，请确认继续下一步开发。")
    else:
        print("❌ 测试失败！请检查错误信息并修复问题。")
    print("=" * 60)


if __name__ == "__main__":
    main()
