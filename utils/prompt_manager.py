"""
提示词管理器 - 集成prompt_manager和prompt_store
"""

import json
from pathlib import Path
from typing import Dict, Any, Optional

# 导入现有的prompt_manager
import sys
prompt_manager_path = str(Path(__file__).parent.parent / "prompt_manager")
if prompt_manager_path not in sys.path:
    sys.path.append(prompt_manager_path)

# 暂时不使用CorePromptManager，直接实现功能
# from core import PromptManager as CorePromptManager


class EnhancedPromptManager:
    """增强的提示词管理器"""
    
    def __init__(self):
        """初始化提示词管理器"""
        # self.core_manager = CorePromptManager()  # 暂时不使用
        self.prompt_store_path = Path(__file__).parent.parent / "prompt_store"
        self.cache = {}

        print("[PROMPT_MANAGER] 增强提示词管理器初始化完成")
    
    def load_agent_prompts(self, agent_name: str) -> Dict[str, str]:
        """
        加载指定agent的所有提示词
        
        Args:
            agent_name: agent名称，如 "product_decomposer"
            
        Returns:
            Dict[str, str]: 提示词字典
        """
        
        agent_path = self.prompt_store_path / agent_name
        
        if not agent_path.exists():
            print(f"[PROMPT_MANAGER] 警告: agent路径不存在: {agent_path}")
            return {}
        
        # 检查缓存
        cache_key = f"agent_{agent_name}"
        if cache_key in self.cache:
            return self.cache[cache_key]
        
        prompts = {}
        
        # 加载配置文件
        config_file = agent_path / "config.json"
        if config_file.exists():
            with open(config_file, 'r', encoding='utf-8') as f:
                config = json.load(f)
            
            # 加载配置中指定的文件
            for key, filename in config.items():
                file_path = agent_path / filename
                if file_path.exists():
                    prompts[key] = file_path.read_text(encoding='utf-8')
                else:
                    print(f"[PROMPT_MANAGER] 警告: 文件不存在: {file_path}")
        
        # 加载system_message
        system_message_file = agent_path / "system_message.md"
        if system_message_file.exists():
            prompts['system_message'] = system_message_file.read_text(encoding='utf-8')
        
        # 缓存结果
        self.cache[cache_key] = prompts
        
        print(f"[PROMPT_MANAGER] 加载了{len(prompts)}个提示词文件: {agent_name}")
        return prompts
    
    def build_system_message(self, agent_name: str, **kwargs) -> str:
        """
        构建agent的system message
        
        Args:
            agent_name: agent名称
            **kwargs: 额外的模板变量
            
        Returns:
            str: 完整的system message
        """
        
        prompts = self.load_agent_prompts(agent_name)
        
        if 'system_message' not in prompts:
            print(f"[PROMPT_MANAGER] 警告: 未找到system_message: {agent_name}")
            return f"你是一个专业的{agent_name}助手。"
        
        system_message = prompts['system_message']
        
        # 替换模板变量
        for key, value in prompts.items():
            if key != 'system_message':
                placeholder = "{" + key + "}"
                if placeholder in system_message:
                    system_message = system_message.replace(placeholder, value)
        
        # 替换额外的变量
        for key, value in kwargs.items():
            placeholder = "{" + key + "}"
            if placeholder in system_message:
                system_message = system_message.replace(placeholder, str(value))
        
        return system_message
    
    def build_user_prompt(self, agent_name: str, template_name: str, **kwargs) -> str:
        """
        构建用户提示词
        
        Args:
            agent_name: agent名称
            template_name: 模板名称
            **kwargs: 模板变量
            
        Returns:
            str: 完整的用户提示词
        """
        
        prompts = self.load_agent_prompts(agent_name)
        
        if template_name not in prompts:
            print(f"[PROMPT_MANAGER] 警告: 未找到模板: {template_name}")
            return ""
        
        template = prompts[template_name]
        
        # 替换模板变量
        for key, value in kwargs.items():
            placeholder = "{" + key + "}"
            if placeholder in template:
                template = template.replace(placeholder, str(value))
        
        return template
    
    def get_decomposition_prompt(self, product_name: str, product_category: Optional[str] = None, 
                               product_description: Optional[str] = None) -> str:
        """
        获取产品拆解提示词
        
        Args:
            product_name: 产品名称
            product_category: 产品类别
            product_description: 产品描述
            
        Returns:
            str: 完整的拆解提示词
        """
        
        base_prompt = f"""
Please decompose the following biomedical product name into SEO keyword seed words.

Product Name: {product_name}
"""

        if product_category:
            base_prompt += f"Product Category: {product_category}\n"

        if product_description:
            base_prompt += f"Product Description: {product_description}\n"

        instructions = """
## Decomposition Requirements

### SEO Objectives
These seed words will be used for:
1. **Google Keywords Planner API queries** - To obtain search volume, competition, CPC data
2. **SEO keyword optimization** - To improve product search rankings
3. **Google Ads campaigns** - For precise keyword advertising

### Decomposition Rules
1. **Core technical terms priority** - Rapid Test, ELISA, PCR, Immunochromatography, etc.
2. **Target molecule names** - IgE, IgG, IgM, Campylobacter, Brucella, etc.
3. **Product type keywords** - Test Kit, Rapid Test, Antibody Test, Allergy Test, etc.
4. **Technical specification parameters** - 10-15 minutes, Whole Blood, Serum, etc.
5. **Application area terms** - Allergy Testing, Infectious Disease, Point-of-Care, etc.

### SEO Optimization Principles
- Extract terms users actually search for
- Avoid brand-specific terminology (unless industry standard)
- Include keywords with commercial purchase intent
- Balance popular keywords and long-tail keywords
- Maintain professionalism and accuracy of biomedical terminology

### Output Format
Please return in JSON format with the following fields:
```json
{
    "seed_words": [
        {
            "word": "keyword",
            "confidence": 0.95,
            "importance": "high",
            "seo_category": "technique",
            "search_intent": "commercial"
        }
    ]
}
```

Field Descriptions:
- **word**: Seed word (2-30 characters)
- **confidence**: Confidence level (0.0-1.0)
- **importance**: Importance level (high/medium/low)
- **seo_category**: SEO classification (technique/target_molecule/product_type/specification/application)
- **search_intent**: Search intent (commercial/informational/navigational)

### Quality Requirements
- Maximum 10 seed words per product
- Sort by importance and confidence
- Ensure technical terms are spelled correctly
- Prioritize high commercial value keywords
- Avoid duplicate or near-duplicate terms

Please strictly follow the above requirements for decomposition, ensuring the generated seed words have high SEO value.
"""
        
        return base_prompt + instructions
    
    def clear_cache(self):
        """清理缓存"""
        self.cache.clear()
        print("[PROMPT_MANAGER] Cache cleared")


# 全局实例
prompt_manager = EnhancedPromptManager()


def get_product_decomposer_system_message() -> str:
    """获取产品拆解器的system message"""
    return prompt_manager.build_system_message("product_decomposer")


def get_decomposition_prompt(product_name: str, product_category: Optional[str] = None, 
                           product_description: Optional[str] = None) -> str:
    """获取产品拆解提示词"""
    return prompt_manager.get_decomposition_prompt(product_name, product_category, product_description)
