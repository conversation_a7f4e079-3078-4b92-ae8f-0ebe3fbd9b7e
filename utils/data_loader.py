"""
数据加载工具（不使用try-except）
"""

import pandas as pd
import chardet
from pathlib import Path
from typing import List, Tuple, Union, Optional

from utils.data_structures import ProductData, ValidationResult, validate_product_name


class DataLoader:
    """数据加载器（不使用try-except）"""
    
    def __init__(self):
        """初始化数据加载器"""
        self.supported_formats = ['.csv', '.xlsx', '.xls']
        print("[DATA_LOADER] 数据加载器初始化完成")
    
    def load_products_from_file(self, file_path: str) -> Tuple[bool, List[ProductData], List[str]]:
        """
        从文件加载产品数据
        
        Args:
            file_path: 文件路径
            
        Returns:
            Tuple[bool, List[ProductData], List[str]]: (成功状态, 产品列表, 错误信息)
        """
        
        print(f"[DATA_LOADER] 开始加载文件: {file_path}")
        
        # 验证文件路径
        validation_success, validation_errors = self._validate_file_path(file_path)
        if not validation_success:
            return False, [], validation_errors
        
        # 检测文件格式
        file_format = self._detect_file_format(file_path)
        if not file_format:
            return False, [], [f"不支持的文件格式，支持的格式: {self.supported_formats}"]
        
        # 根据格式加载数据
        if file_format == '.csv':
            load_success, df, load_errors = self._load_csv_file(file_path)
        else:
            load_success, df, load_errors = self._load_excel_file(file_path)
        
        if not load_success:
            return False, [], load_errors
        
        # 验证数据结构
        validation_result = self._validate_dataframe_structure(df)
        if not validation_result.is_valid:
            return False, [], validation_result.errors
        
        # 转换为产品对象
        products = self._convert_to_products(validation_result.cleaned_data)
        
        print(f"[DATA_LOADER] 成功加载{len(products)}个产品")
        return True, products, validation_result.warnings
    
    def _validate_file_path(self, file_path: str) -> Tuple[bool, List[str]]:
        """验证文件路径"""
        errors = []
        
        if not file_path:
            errors.append("文件路径不能为空")
            return False, errors
        
        path_obj = Path(file_path)
        
        if not path_obj.exists():
            errors.append(f"文件不存在: {file_path}")
        
        if not path_obj.is_file():
            errors.append(f"路径不是文件: {file_path}")
        
        # 检查文件大小
        if path_obj.exists():
            file_size = path_obj.stat().st_size
            if file_size == 0:
                errors.append("文件为空")
            elif file_size > 100 * 1024 * 1024:  # 100MB限制
                errors.append("文件过大，超过100MB限制")
        
        return len(errors) == 0, errors
    
    def _detect_file_format(self, file_path: str) -> Optional[str]:
        """检测文件格式"""
        suffix = Path(file_path).suffix.lower()
        return suffix if suffix in self.supported_formats else None
    
    def _detect_file_encoding(self, file_path: str) -> str:
        """检测文件编码"""
        # 读取文件的前几KB来检测编码
        with open(file_path, 'rb') as f:
            raw_data = f.read(10240)  # 读取10KB
        
        # 使用chardet检测编码
        result = chardet.detect(raw_data)
        encoding = result.get('encoding', 'utf-8')
        
        # 如果检测结果不可靠，使用常见编码
        if result.get('confidence', 0) < 0.7:
            # 尝试常见编码
            common_encodings = ['utf-8', 'gbk', 'gb2312', 'latin1']
            for enc in common_encodings:
                if self._test_encoding(file_path, enc):
                    encoding = enc
                    break
        
        print(f"[DATA_LOADER] 检测到文件编码: {encoding}")
        return encoding
    
    def _test_encoding(self, file_path: str, encoding: str) -> bool:
        """测试编码是否有效（不使用try-except）"""
        # 简单测试：尝试读取文件的前几行
        with open(file_path, 'r', encoding=encoding, errors='ignore') as f:
            lines = f.readlines(5)  # 读取前5行
        
        # 检查是否有明显的编码错误标志
        content = ''.join(lines)
        error_indicators = ['�', '\ufffd']  # 常见的编码错误字符
        
        for indicator in error_indicators:
            if indicator in content:
                return False
        
        return True
    
    def _load_csv_file(self, file_path: str) -> Tuple[bool, pd.DataFrame, List[str]]:
        """加载CSV文件（不使用try-except）"""
        
        # 检测编码
        encoding = self._detect_file_encoding(file_path)
        
        # 使用pandas的错误处理参数
        df = pd.read_csv(
            file_path,
            encoding=encoding,
            on_bad_lines='skip',  # 跳过错误行
            encoding_errors='ignore',  # 忽略编码错误
            dtype=str  # 所有列都作为字符串读取
        )
        
        if df.empty:
            return False, pd.DataFrame(), ["CSV文件为空或无有效数据"]
        
        print(f"[DATA_LOADER] CSV文件加载成功，共{len(df)}行数据")
        return True, df, []
    
    def _load_excel_file(self, file_path: str) -> Tuple[bool, pd.DataFrame, List[str]]:
        """加载Excel文件（不使用try-except）"""
        
        # 使用pandas读取Excel，指定参数处理错误
        df = pd.read_excel(
            file_path,
            dtype=str,  # 所有列都作为字符串读取
            na_filter=False  # 不将空值转换为NaN
        )
        
        if df.empty:
            return False, pd.DataFrame(), ["Excel文件为空或无有效数据"]
        
        print(f"[DATA_LOADER] Excel文件加载成功，共{len(df)}行数据")
        return True, df, []
    
    def _validate_dataframe_structure(self, df: pd.DataFrame) -> ValidationResult:
        """验证DataFrame结构"""
        
        result = ValidationResult(is_valid=True)
        
        # 检查必需的列
        required_columns = ['product_unified_name']
        optional_columns = ['product_category', 'product_description']
        
        # 检查列名（不区分大小写）
        df_columns_lower = [col.lower() for col in df.columns]
        
        # 查找产品名称列
        name_column = None
        for col in df.columns:
            if col.lower() in ['product_unified_name', 'product_name', 'name', '产品名称', '产品统一名称']:
                name_column = col
                break
        
        if not name_column:
            result.is_valid = False
            result.errors.append("未找到产品名称列，支持的列名: product_unified_name, product_name, name, 产品名称")
            return result
        
        # 查找可选列
        category_column = None
        description_column = None
        
        for col in df.columns:
            col_lower = col.lower()
            if col_lower in ['product_category', 'category', '产品类别', '类别']:
                category_column = col
            elif col_lower in ['product_description', 'description', '产品描述', '描述']:
                description_column = col
        
        # 清理数据
        cleaned_df = df.copy()
        
        # 重命名列
        column_mapping = {name_column: 'product_unified_name'}
        if category_column:
            column_mapping[category_column] = 'product_category'
        if description_column:
            column_mapping[description_column] = 'product_description'
        
        cleaned_df = cleaned_df.rename(columns=column_mapping)
        
        # 只保留需要的列
        keep_columns = ['product_unified_name']
        if category_column:
            keep_columns.append('product_category')
        if description_column:
            keep_columns.append('product_description')
        
        cleaned_df = cleaned_df[keep_columns]
        
        # 清理数据
        cleaned_df = self._clean_dataframe(cleaned_df, result)
        
        result.cleaned_data = cleaned_df
        return result
    
    def _clean_dataframe(self, df: pd.DataFrame, result: ValidationResult) -> pd.DataFrame:
        """清理DataFrame数据"""
        
        # 移除空行
        initial_rows = len(df)
        df = df.dropna(subset=['product_unified_name'])
        df = df[df['product_unified_name'].str.strip() != '']
        
        removed_rows = initial_rows - len(df)
        if removed_rows > 0:
            result.warnings.append(f"移除了{removed_rows}行空数据")
        
        # 清理文本数据
        text_columns = ['product_unified_name', 'product_category', 'product_description']
        for col in text_columns:
            if col in df.columns:
                # 转换为字符串并清理
                df[col] = df[col].astype(str)
                df[col] = df[col].str.strip()
                # 移除多余的空白字符
                df[col] = df[col].str.replace(r'\s+', ' ', regex=True)
        
        # 验证产品名称
        invalid_names = []
        for idx, name in df['product_unified_name'].items():
            name_valid, name_errors = validate_product_name(name)
            if not name_valid:
                invalid_names.append(f"行{idx+1}: {name} - {'; '.join(name_errors)}")
        
        if invalid_names:
            result.warnings.extend([f"产品名称验证警告: {warning}" for warning in invalid_names[:10]])  # 只显示前10个
            if len(invalid_names) > 10:
                result.warnings.append(f"还有{len(invalid_names)-10}个产品名称验证警告...")
        
        # 去重
        initial_count = len(df)
        df = df.drop_duplicates(subset=['product_unified_name'])
        duplicates_removed = initial_count - len(df)
        
        if duplicates_removed > 0:
            result.warnings.append(f"移除了{duplicates_removed}个重复产品")
        
        return df
    
    def _convert_to_products(self, df: pd.DataFrame) -> List[ProductData]:
        """将DataFrame转换为产品对象列表"""
        
        products = []
        
        for idx, row in df.iterrows():
            product = ProductData(
                name=row['product_unified_name'],
                category=row.get('product_category', None),
                description=row.get('product_description', None),
                source_file=None,  # 可以后续设置
                row_index=idx
            )
            products.append(product)
        
        return products


def load_products_from_file(file_path: str) -> Tuple[bool, List[ProductData], List[str]]:
    """
    从文件加载产品数据（主要函数）
    
    Args:
        file_path: 文件路径
        
    Returns:
        Tuple[bool, List[ProductData], List[str]]: (成功状态, 产品列表, 错误信息)
    """
    loader = DataLoader()
    return loader.load_products_from_file(file_path)
