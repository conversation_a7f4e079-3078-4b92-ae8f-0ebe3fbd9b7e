"""
数据结构定义
"""

from dataclasses import dataclass, field
from typing import List, Optional, Any, Dict, Tuple
from datetime import datetime


@dataclass
class ProductData:
    """产品数据结构"""
    name: str
    category: Optional[str] = None
    description: Optional[str] = None
    source_file: Optional[str] = None
    row_index: Optional[int] = None


@dataclass
class SeedWord:
    """种子词数据结构"""
    seed_word: str
    source_product: str
    confidence: float
    extraction_method: str
    timestamp: str
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class ExpandedKeyword:
    """扩展关键词数据结构"""
    seed_word: str
    keyword: str
    search_volume: Optional[int]
    competition: Optional[str]
    cpc: Optional[float]
    source: str
    timestamp: str
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class AssociatedKeyword:
    """联想关键词数据结构"""
    original_keyword: str
    associated_keyword: str
    scenario: str
    relevance_score: float
    association_type: str
    timestamp: str
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class ProcessingResult:
    """处理结果基础结构"""
    success: bool
    data: Any = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    processing_time: Optional[float] = None
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class DecompositionResult:
    """产品拆解结果"""
    success: bool
    seed_words: List[SeedWord] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    processing_stats: Dict[str, Any] = field(default_factory=dict)
    timestamp: str = field(default_factory=lambda: datetime.now().isoformat())


@dataclass
class ValidationResult:
    """数据验证结果"""
    is_valid: bool
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    cleaned_data: Any = None


@dataclass
class APICallRecord:
    """API调用记录"""
    timestamp: str
    api_type: str  # "gemini" or "google_ads"
    api_key_id: str
    operation: str
    success: bool
    response_time: Optional[float] = None
    error_message: Optional[str] = None
    request_data: Optional[Dict[str, Any]] = None
    response_data: Optional[Dict[str, Any]] = None
    
    def __post_init__(self):
        if not self.timestamp:
            self.timestamp = datetime.now().isoformat()


@dataclass
class BatchProcessingResult:
    """批量处理结果"""
    total_items: int
    processed_items: int
    successful_items: int
    failed_items: int
    results: List[Any] = field(default_factory=list)
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    processing_time: Optional[float] = None
    start_time: str = field(default_factory=lambda: datetime.now().isoformat())
    end_time: Optional[str] = None
    
    def mark_completed(self):
        """标记批量处理完成"""
        self.end_time = datetime.now().isoformat()
    
    def get_success_rate(self) -> float:
        """获取成功率"""
        if self.processed_items == 0:
            return 0.0
        return self.successful_items / self.processed_items
    
    def add_result(self, result: Any, success: bool = True):
        """添加处理结果"""
        self.results.append(result)
        self.processed_items += 1
        if success:
            self.successful_items += 1
        else:
            self.failed_items += 1


@dataclass
class WorkflowState:
    """工作流状态"""
    current_stage: str
    stage_progress: float
    total_progress: float
    start_time: str
    last_update: str
    stages_completed: List[str] = field(default_factory=list)
    current_batch: Optional[int] = None
    total_batches: Optional[int] = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
    
    def __post_init__(self):
        if not self.start_time:
            self.start_time = datetime.now().isoformat()
        if not self.last_update:
            self.last_update = datetime.now().isoformat()
    
    def update_progress(self, stage: str, progress: float, message: str = ""):
        """更新进度"""
        self.current_stage = stage
        self.stage_progress = progress
        self.last_update = datetime.now().isoformat()
        if message:
            print(f"[WORKFLOW] {stage}: {message} ({progress:.1%})")
    
    def complete_stage(self, stage: str):
        """完成阶段"""
        if stage not in self.stages_completed:
            self.stages_completed.append(stage)
        self.last_update = datetime.now().isoformat()
        print(f"[WORKFLOW] 阶段完成: {stage}")
    
    def add_error(self, error: str):
        """添加错误"""
        self.errors.append(error)
        self.last_update = datetime.now().isoformat()
        print(f"[WORKFLOW] 错误: {error}")
    
    def add_warning(self, warning: str):
        """添加警告"""
        self.warnings.append(warning)
        self.last_update = datetime.now().isoformat()
        print(f"[WORKFLOW] 警告: {warning}")


def create_seed_word(word: str, product: str, confidence: float = 1.0, 
                    method: str = "llm_extraction") -> SeedWord:
    """创建种子词对象"""
    return SeedWord(
        seed_word=word.strip(),
        source_product=product,
        confidence=confidence,
        extraction_method=method,
        timestamp=datetime.now().isoformat()
    )


def create_product_data(name: str, category: str = None, description: str = None) -> ProductData:
    """创建产品数据对象"""
    return ProductData(
        name=name.strip(),
        category=category.strip() if category else None,
        description=description.strip() if description else None
    )


def validate_seed_word(seed_word: str) -> Tuple[bool, List[str]]:
    """验证种子词"""
    errors = []
    
    if not seed_word or not seed_word.strip():
        errors.append("种子词不能为空")
    
    if len(seed_word.strip()) < 2:
        errors.append("种子词长度至少为2个字符")
    
    if len(seed_word.strip()) > 50:
        errors.append("种子词长度不能超过50个字符")
    
    # 检查是否包含特殊字符
    invalid_chars = ['<', '>', '&', '"', "'", '\\', '/', '|']
    for char in invalid_chars:
        if char in seed_word:
            errors.append(f"种子词不能包含特殊字符: {char}")
    
    return len(errors) == 0, errors


def validate_product_name(product_name: str) -> Tuple[bool, List[str]]:
    """验证产品名称"""
    errors = []
    
    if not product_name or not product_name.strip():
        errors.append("产品名称不能为空")
    
    if len(product_name.strip()) < 3:
        errors.append("产品名称长度至少为3个字符")
    
    if len(product_name.strip()) > 200:
        errors.append("产品名称长度不能超过200个字符")
    
    return len(errors) == 0, errors
