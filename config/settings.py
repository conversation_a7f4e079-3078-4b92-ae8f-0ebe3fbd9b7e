"""
配置加载和管理模块（不使用try-except）
"""

import toml
from pathlib import Path
from typing import Dict, Any, Optional, Tuple, List
from dataclasses import dataclass


@dataclass
class GeminiAPIConfig:
    """Gemini API配置（支持轮换）"""
    api_keys: List[str]
    base_url: str
    model: str
    max_tokens: int
    temperature: float
    timeout: int
    auto_rotation: bool
    rotation_on_quota_exceeded: bool
    rotation_on_error: bool


@dataclass
class ProcessingConfig:
    """处理配置"""
    batch_size: int
    max_concurrent: int
    timeout_per_batch: int


@dataclass
class StorageConfig:
    """存储配置"""
    directories: Dict[str, str]
    formats: Dict[str, str]
    cleanup: Dict[str, Any]


class ConfigurationManager:
    """配置管理器（不使用try-except）"""
    
    def __init__(self, config_path: str = "deployment.config.toml"):
        """初始化配置管理器"""
        self.config_path = config_path
        self.config_data = None
    
    def load_configuration(self) -> Tuple[bool, Dict[str, Any], List[str]]:
        """加载配置文件（不使用try-except）"""
        # 检查文件是否存在
        if not Path(self.config_path).exists():
            return False, {}, [f"配置文件不存在: {self.config_path}"]
        
        # 检查文件是否可读
        if not Path(self.config_path).is_file():
            return False, {}, [f"配置路径不是文件: {self.config_path}"]
        
        # 读取文件内容
        content = Path(self.config_path).read_text(encoding='utf-8')
        if not content.strip():
            return False, {}, ["配置文件为空"]
        
        # 解析TOML
        config_data = toml.loads(content)
        
        # 验证配置结构
        validation_success, validation_errors = self.validate_configuration(config_data)
        if not validation_success:
            return False, {}, validation_errors
        
        self.config_data = config_data
        return True, config_data, []
    
    def get_gemini_api_config(self) -> Tuple[bool, Optional[GeminiAPIConfig], List[str]]:
        """获取Gemini API配置"""
        if not self.config_data:
            return False, None, ["配置未加载"]
        
        gemini_config = self.config_data.get("api_configuration", {}).get("google_gemini", {})
        
        # 验证必需字段
        required_fields = ["api_keys", "base_url", "model"]
        missing_fields = [field for field in required_fields if field not in gemini_config]
        if missing_fields:
            return False, None, [f"Gemini API配置缺少字段: {missing_fields}"]
        
        # 验证API密钥
        api_keys = gemini_config.get("api_keys", [])
        if not api_keys or len(api_keys) < 2:
            return False, None, ["至少需要2个Gemini API密钥"]
        
        # 创建配置对象
        config = GeminiAPIConfig(
            api_keys=api_keys,
            base_url=gemini_config.get("base_url", ""),
            model=gemini_config.get("model", "gemini-2.5-flash"),
            max_tokens=gemini_config.get("max_tokens", 4096),
            temperature=gemini_config.get("temperature", 0.7),
            timeout=gemini_config.get("timeout", 60),
            auto_rotation=gemini_config.get("auto_rotation", True),
            rotation_on_quota_exceeded=gemini_config.get("rotation_on_quota_exceeded", True),
            rotation_on_error=gemini_config.get("rotation_on_error", True)
        )
        
        return True, config, []
    
    def get_processing_config(self) -> Tuple[bool, Optional[ProcessingConfig], List[str]]:
        """获取处理配置"""
        if not self.config_data:
            return False, None, ["配置未加载"]
        
        processing_config = self.config_data.get("processing", {})
        batch_config = processing_config.get("batch", {})
        
        config = ProcessingConfig(
            batch_size=batch_config.get("size", 10),
            max_concurrent=batch_config.get("max_concurrent", 3),
            timeout_per_batch=batch_config.get("timeout_per_batch", 300)
        )
        
        return True, config, []
    
    def get_storage_config(self) -> Tuple[bool, Optional[StorageConfig], List[str]]:
        """获取存储配置"""
        if not self.config_data:
            return False, None, ["配置未加载"]
        
        storage_config = self.config_data.get("storage", {})
        
        config = StorageConfig(
            directories=storage_config.get("directories", {
                "results": "results",
                "cache": "cache",
                "docs": "docs",
                "temp": "temp",
                "logs": "logs"
            }),
            formats=storage_config.get("formats", {
                "primary_output": "csv",
                "backup_formats": ["json", "xlsx"],
                "encoding": "utf-8"
            }),
            cleanup=storage_config.get("cleanup", {
                "auto_cleanup_temp": True,
                "cleanup_interval_hours": 24,
                "keep_cache_days": 30,
                "keep_logs_days": 7
            })
        )
        
        return True, config, []
    
    def validate_configuration(self, config_data: Dict[str, Any]) -> Tuple[bool, List[str]]:
        """验证配置有效性（不使用try-except）"""
        errors = []
        
        # 检查必需的顶级配置
        required_sections = ["api_configuration", "processing", "storage"]
        for section in required_sections:
            if section not in config_data:
                errors.append(f"缺少配置节: {section}")
        
        # 验证API配置
        api_config = config_data.get("api_configuration", {})
        if "google_gemini" not in api_config:
            errors.append("缺少Gemini API配置")
        
        return len(errors) == 0, errors


def load_configuration(config_path: str = "deployment.config.toml") -> Tuple[bool, Dict[str, Any], List[str]]:
    """加载配置文件（主要函数，不使用try-except）"""
    manager = ConfigurationManager(config_path)
    return manager.load_configuration()


def get_gemini_api_keys() -> List[str]:
    """获取Gemini API密钥列表"""
    return [
        "AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo",
        "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E"
    ]


def get_default_gemini_config() -> GeminiAPIConfig:
    """获取默认Gemini配置"""
    return GeminiAPIConfig(
        api_keys=get_gemini_api_keys(),
        base_url="https://generativelanguage.googleapis.com/v1beta/openai/",
        model="gemini-2.5-flash",
        max_tokens=4096,
        temperature=0.7,
        timeout=60,
        auto_rotation=True,
        rotation_on_quota_exceeded=True,
        rotation_on_error=True
    )
