"""
Gemini API轮换管理器（支持自动轮换）
"""

import time
from datetime import datetime
from typing import List, Optional, Tuple, Set
from dataclasses import dataclass

from autogen_agentchat.agents import AssistantAgent
from autogen_agentchat.messages import TextMessage
from autogen_ext.models.openai import OpenAIChatCompletionClient

from config.settings import GeminiAPIConfig, get_default_gemini_config


@dataclass
class APICallStats:
    """API调用统计"""
    total_calls: int = 0
    successful_calls: int = 0
    failed_calls: int = 0
    last_call_time: Optional[str] = None


class GeminiAPIManager:
    """Gemini API管理器（支持自动轮换）"""
    
    def __init__(self, config: Optional[GeminiAPIConfig] = None):
        """初始化API管理器"""
        self.config = config or get_default_gemini_config()
        self.current_api_index = 0
        self.failed_apis: Set[str] = set()
        self.api_stats = {api_key: APICallStats() for api_key in self.config.api_keys}
        
        print(f"[GEMINI_API] 初始化API管理器，共{len(self.config.api_keys)}个API密钥")
    
    def get_current_api_key(self) -> str:
        """获取当前API密钥"""
        return self.config.api_keys[self.current_api_index]
    
    def get_next_available_api_key(self) -> Optional[str]:
        """获取下一个可用的API密钥"""
        for i, api_key in enumerate(self.config.api_keys):
            if api_key not in self.failed_apis:
                self.current_api_index = i
                return api_key
        return None
    
    def mark_api_as_failed(self, api_key: str, error_msg: str = "") -> None:
        """标记API密钥为失败状态"""
        self.failed_apis.add(api_key)
        self.api_stats[api_key].failed_calls += 1
        print(f"[GEMINI_API] 标记API密钥为失败: {api_key[:20]}... - {error_msg}")
    
    def reset_failed_apis(self) -> None:
        """重置失败的API密钥（用于重试）"""
        self.failed_apis.clear()
        print(f"[GEMINI_API] 重置所有API密钥状态")
    
    def record_successful_call(self, api_key: str) -> None:
        """记录成功的API调用"""
        self.api_stats[api_key].total_calls += 1
        self.api_stats[api_key].successful_calls += 1
        self.api_stats[api_key].last_call_time = datetime.now().isoformat()
    
    def record_failed_call(self, api_key: str) -> None:
        """记录失败的API调用"""
        self.api_stats[api_key].total_calls += 1
        self.api_stats[api_key].failed_calls += 1
        self.api_stats[api_key].last_call_time = datetime.now().isoformat()
    
    def get_api_stats(self) -> dict:
        """获取API使用统计"""
        stats = {}
        for api_key, stat in self.api_stats.items():
            masked_key = api_key[:20] + "..."
            success_rate = 0
            if stat.total_calls > 0:
                success_rate = stat.successful_calls / stat.total_calls
            
            stats[masked_key] = {
                "total_calls": stat.total_calls,
                "successful_calls": stat.successful_calls,
                "failed_calls": stat.failed_calls,
                "success_rate": success_rate,
                "last_call_time": stat.last_call_time,
                "is_failed": api_key in self.failed_apis
            }
        
        return stats
    
    def call_gemini_with_rotation(self, prompt: str, max_retries: int = 2) -> Tuple[bool, str, str]:
        """
        调用Gemini API（支持自动轮换）
        
        返回: (success, response, error_message)
        """
        
        for attempt in range(max_retries + 1):
            # 获取可用的API密钥
            api_key = self.get_next_available_api_key()
            
            if api_key is None:
                if attempt == 0:
                    return False, "", "没有可用的Gemini API密钥"
                else:
                    # 重置失败状态，再试一次
                    self.reset_failed_apis()
                    api_key = self.get_next_available_api_key()
                    if api_key is None:
                        return False, "", "所有Gemini API密钥都不可用"
            
            # 尝试调用API
            call_success, response, error_msg = self._call_single_gemini_api(api_key, prompt)
            
            if call_success:
                self.record_successful_call(api_key)
                print(f"[GEMINI_API] 调用成功，使用API: {api_key[:20]}...")
                return True, response, ""
            
            # 调用失败，记录并标记API
            self.record_failed_call(api_key)
            print(f"[GEMINI_API] API调用失败: {error_msg}")
            
            # 根据错误类型决定是否标记为失败
            if self._should_mark_api_as_failed(error_msg):
                self.mark_api_as_failed(api_key, error_msg)
            
            # 检查是否还有其他可用的API
            remaining_apis = [key for key in self.config.api_keys if key not in self.failed_apis]
            if not remaining_apis and attempt < max_retries:
                print(f"[GEMINI_API] 所有API都失败，等待后重试...")
                time.sleep(2 ** attempt)  # 指数退避
                continue
            elif not remaining_apis:
                break
        
        return False, "", f"所有Gemini API密钥在{max_retries + 1}次尝试后都失败"
    
    def _call_single_gemini_api(self, api_key: str, prompt: str) -> Tuple[bool, str, str]:
        """
        调用单个Gemini API（允许使用try-except）
        """
        try:
            # 创建客户端
            client = OpenAIChatCompletionClient(
                model=self.config.model,
                api_key=api_key,
                base_url=self.config.base_url
            )
            
            # 发送请求
            messages = [{"role": "user", "content": prompt}]
            response = client.create(
                messages=messages,
                max_tokens=self.config.max_tokens,
                temperature=self.config.temperature
            )
            
            # 处理响应
            if hasattr(response, 'content'):
                content = response.content
            elif hasattr(response, 'choices') and response.choices:
                content = response.choices[0].message.content
            else:
                content = str(response)
            
            if not content or content.strip() == "":
                return False, "", "API返回空响应"
            
            return True, content, ""
            
        except Exception as e:
            error_msg = str(e)
            return False, "", error_msg
    
    def _should_mark_api_as_failed(self, error_msg: str) -> bool:
        """判断是否应该标记API为失败状态"""
        error_lower = error_msg.lower()
        
        # 配额限制 - 应该标记为失败
        if any(keyword in error_lower for keyword in ["quota", "limit exceeded", "usage limit"]):
            return True
        
        # 认证失败 - 应该标记为失败
        if any(keyword in error_lower for keyword in ["auth", "key", "unauthorized", "forbidden"]):
            return True
        
        # 网络错误 - 不标记为失败，可能是临时问题
        if any(keyword in error_lower for keyword in ["network", "connection", "timeout"]):
            return False
        
        # 频率限制 - 不标记为失败，等待后可恢复
        if any(keyword in error_lower for keyword in ["rate limit", "too many requests"]):
            return False
        
        # 服务器错误 - 不标记为失败，可能是临时问题
        if any(keyword in error_lower for keyword in ["server error", "internal error", "503", "502"]):
            return False
        
        # 未知错误 - 标记为失败，避免重复尝试
        return True
    
    def test_api_connection(self) -> Tuple[bool, str]:
        """测试API连接"""
        test_success, test_response, test_error = self.call_gemini_with_rotation("test")
        return test_success, test_error if not test_success else "连接正常"


class AutoGenAgentWithRotation:
    """支持API轮换的AutoGen Agent"""
    
    def __init__(self, config: Optional[GeminiAPIConfig] = None):
        """初始化AutoGen Agent"""
        self.api_manager = GeminiAPIManager(config)
        self.agent = None
        self.current_api_key = None
    
    def initialize_agent(self, system_message: Optional[str] = None) -> Tuple[bool, str]:
        """初始化AutoGen Agent（支持API轮换和自定义system_message）"""

        # 尝试每个可用的API密钥
        for api_key in self.api_manager.config.api_keys:
            if api_key in self.api_manager.failed_apis:
                continue

            init_success, init_message = self._try_initialize_with_api(api_key, system_message)

            if init_success:
                self.current_api_key = api_key
                print(f"[AUTOGEN] 初始化成功，使用API: {api_key[:20]}...")
                return True, "AutoGen Agent初始化成功"
            else:
                print(f"[AUTOGEN] API密钥 {api_key[:20]}... 初始化失败: {init_message}")
                self.api_manager.mark_api_as_failed(api_key, init_message)

        return False, "所有Gemini API密钥都无法初始化AutoGen Agent"
    
    def _try_initialize_with_api(self, api_key: str, system_message: Optional[str] = None) -> Tuple[bool, str]:
        """尝试使用指定API密钥初始化（允许使用try-except）"""
        try:
            # 创建模型客户端
            model_client = OpenAIChatCompletionClient(
                model=self.api_manager.config.model,
                api_key=api_key,
                base_url=self.api_manager.config.base_url
            )

            # 使用自定义system_message或默认消息
            if system_message is None:
                system_message = "你是一个专业的生物医学关键词处理专家。请准确地分析和处理用户提供的产品名称。"

            # 创建AssistantAgent
            self.agent = AssistantAgent(
                name="keyword_processor",
                model_client=model_client,
                system_message=system_message
            )

            # 测试API连接
            test_success, test_error = self._test_agent_connection()
            if not test_success:
                return False, test_error

            return True, "初始化成功"

        except Exception as e:
            return False, str(e)
    
    def _test_agent_connection(self) -> Tuple[bool, str]:
        """测试Agent连接（允许使用try-except）"""
        try:
            # 发送简单的测试消息
            import asyncio

            async def test_call():
                test_message = TextMessage(content="test", source="user")
                response = await self.agent.on_messages([test_message], cancellation_token=None)
                return response

            # 运行异步测试 - 改进异步处理
            try:
                # 尝试获取现有的事件循环
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果循环正在运行，创建新的任务
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, test_call())
                        response = future.result(timeout=30)
                else:
                    response = loop.run_until_complete(test_call())
            except RuntimeError:
                # 没有事件循环，创建新的
                response = asyncio.run(test_call())

            return True, ""

        except Exception as e:
            return False, str(e)
    
    def call_with_rotation(self, prompt: str) -> Tuple[bool, str, str]:
        """
        调用AutoGen Agent（支持API轮换）
        """
        
        # 检查Agent是否已初始化
        if self.agent is None:
            init_success, init_message = self.initialize_agent()
            if not init_success:
                return False, "", init_message
        
        # 尝试调用当前Agent
        call_success, response, error_msg = self._call_current_agent(prompt)
        
        if call_success:
            return True, response, ""
        
        # 当前API失败，标记并尝试轮换
        print(f"[AUTOGEN] 当前API调用失败: {error_msg}")
        if self.current_api_key:
            self.api_manager.mark_api_as_failed(self.current_api_key, error_msg)
        
        # 重新初始化Agent（自动轮换到下一个可用API）
        print(f"[AUTOGEN] 尝试切换API并重新初始化...")
        reinit_success, reinit_message = self.initialize_agent()
        
        if not reinit_success:
            return False, "", f"API轮换失败: {reinit_message}"
        
        # 使用新API重试
        retry_success, retry_response, retry_error = self._call_current_agent(prompt)
        
        if retry_success:
            print(f"[AUTOGEN] API轮换成功，重试调用成功")
            return True, retry_response, ""
        else:
            return False, "", f"重试失败: {retry_error}"
    
    def _call_current_agent(self, prompt: str) -> Tuple[bool, str, str]:
        """调用当前Agent（允许使用try-except）"""
        try:
            import asyncio

            async def get_response():
                message = TextMessage(content=prompt, source="user")
                response = await self.agent.on_messages([message], cancellation_token=None)

                # 处理响应 - 修复响应处理逻辑
                if hasattr(response, 'messages') and response.messages:
                    # 获取最后一条消息
                    last_message = response.messages[-1]
                    if hasattr(last_message, 'content'):
                        return last_message.content
                    else:
                        return str(last_message)
                elif hasattr(response, 'chat_message') and hasattr(response.chat_message, 'content'):
                    return response.chat_message.content
                elif hasattr(response, 'content'):
                    return response.content
                elif hasattr(response, 'text'):
                    return response.text
                else:
                    return str(response)

            # 运行异步函数 - 改进异步处理
            try:
                # 尝试获取现有的事件循环
                loop = asyncio.get_event_loop()
                if loop.is_running():
                    # 如果循环正在运行，创建新的任务
                    import concurrent.futures
                    with concurrent.futures.ThreadPoolExecutor() as executor:
                        future = executor.submit(asyncio.run, get_response())
                        response = future.result(timeout=60)
                else:
                    response = loop.run_until_complete(get_response())
            except RuntimeError:
                # 没有事件循环，创建新的
                response = asyncio.run(get_response())

            return True, response, ""

        except Exception as e:
            return False, "", str(e)
