#!/usr/bin/env python3
"""
完整处理 source/backed.csv 文件中的所有产品
分批处理以确保稳定性和API限制
"""

import sys
import time
from pathlib import Path
from datetime import datetime

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from process_backed_csv import load_backed_csv, process_batch, save_results_to_csv, save_results_to_json
from utils.data_structures import DecompositionResult


def process_all_products(batch_size: int = 50, start_from: int = 0, delay_between_batches: int = 30):
    """
    处理所有产品
    
    Args:
        batch_size: 每批处理的产品数量
        start_from: 从第几个产品开始处理
        delay_between_batches: 批次间延迟（秒）
    """
    
    print("🔬 Keyword Architect Agent - Processing ALL backed.csv products")
    print("=" * 70)
    print(f"Batch size: {batch_size}")
    print(f"Start from product: {start_from + 1}")
    print(f"Delay between batches: {delay_between_batches} seconds")
    print()
    
    # 加载所有产品
    print("Loading all products...")
    products = load_backed_csv()
    
    if not products:
        print("❌ No products loaded. Exiting.")
        return
    
    total_products = len(products)
    print(f"📊 Total products to process: {total_products}")
    
    # 计算批次信息
    remaining_products = total_products - start_from
    total_batches = (remaining_products + batch_size - 1) // batch_size
    
    print(f"📊 Processing plan:")
    print(f"   - Starting from product {start_from + 1}")
    print(f"   - Remaining products: {remaining_products}")
    print(f"   - Total batches: {total_batches}")
    print(f"   - Estimated time: {total_batches * (batch_size * 8 + delay_between_batches) / 60:.1f} minutes")
    print()
    
    # 初始化累积结果
    all_seed_words = []
    total_successful = 0
    total_failed = 0
    processing_stats = {}
    
    # 分批处理
    for batch_num in range(total_batches):
        batch_start = start_from + batch_num * batch_size
        batch_end = min(batch_start + batch_size, total_products)
        
        print(f"\n{'='*50}")
        print(f"🔄 BATCH {batch_num + 1}/{total_batches}")
        print(f"Processing products {batch_start + 1} to {batch_end}")
        print(f"{'='*50}")
        
        # 处理当前批次
        try:
            result = process_batch(products, batch_size, batch_start)
            
            if result.success:
                # 累积结果
                all_seed_words.extend(result.seed_words)
                total_successful += result.processing_stats.get('successful_decompositions', 0)
                total_failed += result.processing_stats.get('failed_decompositions', 0)
                
                print(f"✅ Batch {batch_num + 1} completed successfully!")
                print(f"   - Products processed: {len(result.seed_words)} seed words generated")
                print(f"   - Success rate: {result.processing_stats.get('success_rate', 0):.1%}")
                
                # 保存中间结果（追加模式）
                if batch_num == 0:
                    # 第一批：覆盖模式
                    save_results_to_csv(result, append_mode=False)
                else:
                    # 后续批次：追加模式
                    save_results_to_csv(result, append_mode=True)
                
            else:
                print(f"❌ Batch {batch_num + 1} failed!")
                for error in result.errors:
                    print(f"   Error: {error}")
                total_failed += batch_size
        
        except Exception as e:
            print(f"❌ Batch {batch_num + 1} encountered an error: {e}")
            total_failed += batch_size
        
        # 显示总体进度
        processed_so_far = min(batch_end, total_products)
        progress = processed_so_far / total_products
        print(f"\n📊 Overall Progress: {processed_so_far}/{total_products} ({progress:.1%})")
        print(f"   - Total successful: {total_successful}")
        print(f"   - Total failed: {total_failed}")
        print(f"   - Total seed words: {len(all_seed_words)}")
        
        # 批次间延迟（除了最后一批）
        if batch_num < total_batches - 1:
            print(f"\n⏳ Waiting {delay_between_batches} seconds before next batch...")
            time.sleep(delay_between_batches)
    
    # 创建最终结果
    final_result = DecompositionResult(
        success=total_successful > 0,
        seed_words=all_seed_words,
        processing_stats={
            "total_products": total_products,
            "successful_decompositions": total_successful,
            "failed_decompositions": total_failed,
            "total_seed_words": len(all_seed_words),
            "success_rate": total_successful / total_products if total_products > 0 else 0,
            "avg_seed_words_per_product": len(all_seed_words) / total_successful if total_successful > 0 else 0,
            "timestamp": datetime.now().isoformat(),
            "processing_method": "batch_processing"
        }
    )
    
    # 保存最终JSON结果
    save_results_to_json(final_result)
    
    # 显示最终统计
    print(f"\n{'='*70}")
    print("🎉 FINAL RESULTS")
    print(f"{'='*70}")
    print(f"✅ Processing completed!")
    print(f"📊 Final Statistics:")
    print(f"   - Total products: {total_products}")
    print(f"   - Successfully processed: {total_successful}")
    print(f"   - Failed: {total_failed}")
    print(f"   - Success rate: {total_successful / total_products:.1%}")
    print(f"   - Total seed words generated: {len(all_seed_words)}")
    print(f"   - Average seed words per product: {len(all_seed_words) / total_successful:.1f}")
    print()
    print(f"📁 Results saved to:")
    print(f"   - CSV: results/backed_seed_words.csv")
    print(f"   - JSON: results/backed_seed_words.json")
    print(f"{'='*70}")
    
    return final_result


def main():
    """主函数"""
    
    # 解析命令行参数
    batch_size = 50  # 默认批次大小
    start_from = 0   # 默认从第一个产品开始
    delay = 30       # 默认延迟30秒
    
    if len(sys.argv) > 1:
        try:
            batch_size = int(sys.argv[1])
        except ValueError:
            print(f"Invalid batch size: {sys.argv[1]}, using default: {batch_size}")
    
    if len(sys.argv) > 2:
        try:
            start_from = int(sys.argv[2])
        except ValueError:
            print(f"Invalid start index: {sys.argv[2]}, using default: {start_from}")
    
    if len(sys.argv) > 3:
        try:
            delay = int(sys.argv[3])
        except ValueError:
            print(f"Invalid delay: {sys.argv[3]}, using default: {delay}")
    
    # 确认开始处理
    print(f"About to process ALL products with:")
    print(f"  - Batch size: {batch_size}")
    print(f"  - Start from: {start_from}")
    print(f"  - Delay between batches: {delay} seconds")
    print()
    
    response = input("Continue? (y/N): ").strip().lower()
    if response != 'y':
        print("Processing cancelled.")
        return
    
    # 开始处理
    start_time = datetime.now()
    result = process_all_products(batch_size, start_from, delay)
    end_time = datetime.now()
    
    processing_time = (end_time - start_time).total_seconds()
    print(f"\n⏱️  Total processing time: {processing_time / 60:.1f} minutes")
    
    if result and result.success:
        print("🎉 All products processed successfully!")
    else:
        print("⚠️  Processing completed with some failures.")


if __name__ == "__main__":
    main()
