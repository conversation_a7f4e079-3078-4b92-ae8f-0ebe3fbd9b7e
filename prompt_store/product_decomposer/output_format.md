## Output Format Requirements

### JSON Structure
```json
{
    "seed_words": [
        {
            "word": "Rapid Test",
            "confidence": 0.95,
            "importance": "high",
            "seo_category": "technique",
            "search_intent": "commercial"
        },
        {
            "word": "Allergy Test",
            "confidence": 0.90,
            "importance": "high",
            "seo_category": "product_type",
            "search_intent": "commercial"
        }
    ]
}
```

### Field Descriptions
- **word**: Extracted seed word (required)
- **confidence**: Extraction confidence 0.0-1.0 (required)
- **importance**: Importance level high/medium/low (required)
- **seo_category**: SEO classification (optional)
  - technique: Technical method category
  - target_molecule: Target molecule category
  - product_type: Product type category
  - specification: Specification parameter category
  - application: Application area category
- **search_intent**: Search intent (optional)
  - commercial: Commercial purchase intent
  - informational: Information query intent
  - navigational: Navigation search intent

### Sorting Rules
1. Sort by importance (high > medium > low)
2. Within same importance, sort by confidence (high to low)
3. Prioritize commercial intent keywords
