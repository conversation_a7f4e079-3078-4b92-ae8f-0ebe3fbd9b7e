## 输出格式要求

### JSON结构
```json
{
    "seed_words": [
        {
            "word": "ELISA",
            "confidence": 0.95,
            "importance": "high",
            "seo_category": "technique",
            "search_intent": "informational"
        },
        {
            "word": "IL-6",
            "confidence": 0.90,
            "importance": "high", 
            "seo_category": "target_molecule",
            "search_intent": "commercial"
        }
    ]
}
```

### 字段说明
- **word**: 提取的种子词（必需）
- **confidence**: 提取置信度 0.0-1.0（必需）
- **importance**: 重要性等级 high/medium/low（必需）
- **seo_category**: SEO分类（可选）
  - technique: 技术方法类
  - target_molecule: 目标分子类
  - product_type: 产品类型类
  - specification: 规格参数类
  - application: 应用领域类
- **search_intent**: 搜索意图（可选）
  - commercial: 商业购买意图
  - informational: 信息查询意图
  - navigational: 导航查找意图

### 排序规则
1. 按importance排序（high > medium > low）
2. 相同importance内按confidence排序（高到低）
3. 优先展示commercial intent的词汇
