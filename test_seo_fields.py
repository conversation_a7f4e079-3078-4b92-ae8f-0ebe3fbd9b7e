#!/usr/bin/env python3
"""
测试SEO字段提取
"""

import sys
from pathlib import Path

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from utils.data_structures import ProductData, create_product_data
from modules.product_decomposer import decompose_products


def test_seo_fields():
    """测试SEO字段提取"""
    
    print("🔬 Testing SEO Fields Extraction")
    print("=" * 50)
    
    # 创建测试产品
    test_products = [
        create_product_data(
            name="Rapid Allergy Test Kit",
            category="Allergy Testing",
            description="Quick allergy detection test"
        )
    ]
    
    print(f"Testing product: {test_products[0].name}")
    
    # 执行拆解
    result = decompose_products(test_products)
    
    if result.success:
        print(f"✅ Decomposition successful!")
        print(f"Generated {len(result.seed_words)} seed words:")
        
        for i, seed_word in enumerate(result.seed_words, 1):
            print(f"\n{i}. {seed_word.seed_word}")
            print(f"   Confidence: {seed_word.confidence}")
            print(f"   Importance: {getattr(seed_word, 'importance', 'N/A')}")
            print(f"   SEO Category: {getattr(seed_word, 'seo_category', 'N/A')}")
            print(f"   Search Intent: {getattr(seed_word, 'search_intent', 'N/A')}")
            print(f"   Method: {seed_word.extraction_method}")
    else:
        print(f"❌ Decomposition failed!")
        for error in result.errors:
            print(f"   Error: {error}")


if __name__ == "__main__":
    test_seo_fields()
