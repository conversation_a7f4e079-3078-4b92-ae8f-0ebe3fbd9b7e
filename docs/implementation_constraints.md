# Implementation Constraints - Keyword Architect Agent
# 实现约束说明文档
# Version: 1.0
# Date: 2025-07-17

## 1. 核心约束说明

### 1.1 错误处理约束
**约束**: 除了向大模型API和Google Keyword Planner API进行请求外，其他地方禁止使用try-except模块。

**适用范围**:
- ✅ **允许使用try-except的场景**:
  - Google Gemini API调用
  - Google Keyword Planner API调用
  - AutoGen v0.4框架的LLM调用
  - 外部网络API请求

- ❌ **禁止使用try-except的场景**:
  - 文件操作（读取、写入、删除）
  - 数据处理和验证
  - 配置加载和解析
  - 内部函数调用
  - 数据结构操作
  - 字符串处理
  - 数学计算

**替代方案**:
```python
# ❌ 禁止的写法
try:
    data = pd.read_csv(file_path)
    return data
except Exception as e:
    print(f"Error: {e}")
    return None

# ✅ 推荐的写法
def safe_read_csv(file_path: str) -> <PERSON><PERSON>[bool, Union[pd.DataFrame, str]]:
    # 预检查
    if not Path(file_path).exists():
        return False, f"文件不存在: {file_path}"
    
    if not Path(file_path).is_file():
        return False, f"路径不是文件: {file_path}"
    
    # 使用pandas的内置错误处理
    df = pd.read_csv(file_path, on_bad_lines='skip', encoding_errors='ignore')
    
    if df.empty:
        return False, "文件为空或无有效数据"
    
    return True, df
```

### 1.2 Gemini API轮换约束
**约束**: 在请求Gemini 2.5 Flash模型时，当一个API出现限额时，切换到另一个API上。

**可用API密钥**:
1. `AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo`
2. `AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E`

**轮换策略**:
- 默认使用第一个API密钥
- 当API调用失败时，自动切换到下一个可用的API密钥
- 支持配额耗尽、认证失败、网络错误等场景的自动切换
- 所有API密钥都失败时，返回明确的错误信息

**实现要求**:
```python
# ✅ 正确的实现方式
def call_gemini_with_rotation(prompt: str) -> Tuple[bool, str, str]:
    api_keys = ["AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo", 
                "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E"]
    
    for api_key in api_keys:
        try:  # 仅在API调用时允许使用try-except
            client = OpenAIChatCompletionClient(
                model="gemini-2.5-flash",
                api_key=api_key,
                base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
            )
            response = client.complete(prompt)
            return True, response, ""
        except Exception as e:
            print(f"API密钥 {api_key[:20]}... 调用失败: {str(e)}")
            if api_key != api_keys[-1]:  # 不是最后一个API
                continue
            else:
                return False, "", f"所有API密钥都不可用: {str(e)}"
```

## 2. 架构影响分析

### 2.1 错误处理模式变更
**影响的模块**:
- `config/settings.py` - 配置加载改为返回值模式
- `services/data_loader.py` - 数据加载改为返回值模式
- `services/result_manager.py` - 结果保存改为返回值模式
- `utils/file_utils.py` - 文件操作改为返回值模式
- `utils/validation.py` - 数据验证改为返回值模式

**新的返回值模式**:
```python
# 标准返回值模式
def operation_function() -> Tuple[bool, Union[SuccessType, str]]:
    """
    返回: (success, result_or_error_message)
    - success: bool - 操作是否成功
    - result_or_error_message: 成功时返回结果，失败时返回错误信息
    """
    pass

# 复杂操作的返回值模式
@dataclass
class OperationResult:
    success: bool
    data: Any = None
    errors: List[str] = field(default_factory=list)
    warnings: List[str] = field(default_factory=list)
```

### 2.2 API调用模式变更
**影响的模块**:
- `modules/decomposition.py` - 支持Gemini API轮换
- `modules/llm_association.py` - 支持Gemini API轮换
- `services/keyword_processor.py` - 集成API轮换逻辑

**新的API调用模式**:
```python
class AutoGenAgentWithRotation:
    def __init__(self):
        self.api_keys = ["AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo", 
                        "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E"]
        self.current_api_index = 0
        self.failed_apis = set()
    
    def call_with_rotation(self, prompt: str) -> Tuple[bool, str, str]:
        # 实现API轮换逻辑
        pass
```

## 3. 配置文件更新

### 3.1 deployment.config.toml更新
```toml
# Gemini API配置（支持轮换）
[api_configuration.google_gemini]
base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"
model = "gemini-2.5-flash"
max_tokens = 4096
temperature = 0.7
timeout = 60
retry_attempts = 3
retry_delay = 1
# API密钥轮换配置
api_keys = ["AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo", "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E"]
auto_rotation = true
rotation_on_quota_exceeded = true
rotation_on_error = true
```

### 3.2 环境变量更新
```toml
[environment.variables]
GOOGLE_GEMINI_API_KEY_1 = "AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo"
GOOGLE_GEMINI_API_KEY_2 = "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E"
PYTHONPATH = "."
PYTHONIOENCODING = "utf-8"
```

## 4. 实现指导

### 4.1 错误处理实现指导
1. **预检查优于异常处理**: 在操作前进行充分的验证
2. **明确的返回值**: 使用Tuple[bool, Union[Result, str]]模式
3. **详细的错误信息**: 提供具体的错误描述
4. **日志记录**: 记录所有错误和警告信息

### 4.2 API轮换实现指导
1. **失败检测**: 准确识别API调用失败的原因
2. **智能切换**: 根据错误类型决定是否切换API
3. **状态管理**: 跟踪每个API密钥的可用状态
4. **恢复机制**: 定期重置失败的API密钥状态

### 4.3 测试策略
1. **错误路径测试**: 测试所有可能的错误情况
2. **API轮换测试**: 模拟API失败和切换场景
3. **边界条件测试**: 测试极端情况的处理
4. **集成测试**: 验证整体工作流的稳定性

## 5. 质量保证

### 5.1 代码审查要点
- [ ] 确认没有在非API调用处使用try-except
- [ ] 验证所有函数都有明确的返回值模式
- [ ] 检查Gemini API调用都支持轮换
- [ ] 确认错误信息详细且有用

### 5.2 性能考虑
- API轮换不应显著增加响应时间
- 错误检查不应影响正常流程性能
- 状态管理应该高效且线程安全

### 5.3 可维护性
- 错误处理逻辑应该一致且可预测
- API轮换逻辑应该易于扩展新的API密钥
- 配置应该集中管理且易于修改

## 6. 迁移计划

### 6.1 现有代码迁移
1. 识别所有使用try-except的非API调用代码
2. 重构为返回值模式
3. 更新调用方的错误处理逻辑
4. 添加Gemini API轮换支持

### 6.2 测试迁移
1. 更新单元测试以验证新的返回值模式
2. 添加API轮换的测试用例
3. 验证错误处理的完整性
4. 性能测试确保无回归

### 6.3 文档更新
1. 更新所有相关的技术文档
2. 更新API使用说明
3. 更新部署和配置指南
4. 更新故障排除文档

## 7. 风险评估

### 7.1 技术风险
- **风险**: 返回值模式可能增加代码复杂性
- **缓解**: 使用标准化的返回值结构和工具函数

- **风险**: API轮换可能导致不一致的行为
- **缓解**: 详细的日志记录和状态跟踪

### 7.2 运营风险
- **风险**: API密钥管理的安全性
- **缓解**: 使用环境变量和配置文件分离

- **风险**: API配额管理的复杂性
- **缓解**: 实现详细的配额监控和预警

## 8. 总结

这些约束旨在提高代码的可靠性和可维护性：

1. **错误处理约束**确保了除API调用外的代码更加可预测和易于调试
2. **API轮换约束**提高了系统的可用性和容错能力
3. **标准化的实现模式**确保了代码的一致性和可维护性

所有相关文档已根据这些约束进行更新，确保实现阶段能够严格遵循这些要求。
