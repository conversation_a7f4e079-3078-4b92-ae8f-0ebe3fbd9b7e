# Keyword Architect Agent - Deployment Configuration
# 关键词架构师代理 - 部署配置
# Version: 1.0
# Date: 2025-07-17

[project]
name = "keyword_architect_agent"
version = "1.0.0"
description = "关键词架构师代理 - 生物医学产品关键词研究和扩展工具"
author = "Development Team"
python_version = ">=3.9"

[environment]
# 运行环境配置
runtime = "python"
platform = "linux"
architecture = "x86_64"

# 环境变量
[environment.variables]
GOOGLE_GEMINI_API_KEY = "AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo"
GOOGLE_GEMINI_API_KEY_2 = "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E"
GOOGLE_GEMINI_API_KEY_3 = "AIzaSyBLydBhLMf-_Y4eJ7puw5OAvJD5Uq5Umi0"
PYTHONPATH = "."
PYTHONIOENCODING = "utf-8"

[dependencies]
# 核心依赖
[dependencies.core]
pandas = ">=2.0.0"
numpy = ">=1.24.0"
python-dotenv = ">=1.0.0"
pyyaml = ">=6.0"
pathlib = ">=1.0.0"

# AutoGen v0.4 依赖
[dependencies.autogen]
autogen-agentchat = ">=0.4.0"
autogen-ext = ">=0.4.0"
openai = ">=1.0.0"

# Google APIs 依赖
[dependencies.google]
google-ads = ">=24.0.0"
google-auth = ">=2.0.0"
google-auth-oauthlib = ">=1.0.0"
google-auth-httplib2 = ">=0.2.0"

# 数据处理依赖
[dependencies.data]
openpyxl = ">=3.1.0"
xlsxwriter = ">=3.1.0"
chardet = ">=5.0.0"

[api_configuration]
# Google Gemini API 配置
[api_configuration.google_gemini]
base_url = "https://generativelanguage.googleapis.com/v1beta/openai/"
model = "gemini-2.5-flash"
max_tokens = 4096
temperature = 0.7
timeout = 60
retry_attempts = 3
retry_delay = 1

# Google Keywords Planner API 配置
[api_configuration.google_keywords_planner]
config_file = "google-ads.yaml"
max_calls_per_hour = 10
timeout = 60
retry_attempts = 3
retry_delay = 2
cache_enabled = true

# API 轮换配置
[api_configuration.rotation]
enabled = true
auto_switch = true
switch_on_quota_exceeded = true
switch_on_error = false

[processing]
# 处理配置
[processing.batch]
size = 10
max_concurrent = 3
timeout_per_batch = 300

[processing.decomposition]
max_seed_words_per_product = 10
min_seed_word_length = 2
filter_common_words = true
language = "en"

[processing.expansion]
max_keywords_per_seed = 100
min_search_volume = 10
competition_levels = ["LOW", "MEDIUM", "HIGH"]
include_cpc_data = true

[processing.association]
scenarios = ["clinical_use", "research_application", "commercial_search"]
max_associations_per_keyword = 20
relevance_threshold = 0.7
include_semantic_variants = true

[storage]
# 存储配置
[storage.directories]
results = "results"
cache = "cache"
docs = "docs"
temp = "temp"
logs = "logs"

[storage.formats]
primary_output = "csv"
backup_formats = ["json", "xlsx"]
encoding = "utf-8"
compression = false

[storage.cleanup]
auto_cleanup_temp = true
cleanup_interval_hours = 24
keep_cache_days = 30
keep_logs_days = 7

[cache]
# 缓存配置
[cache.settings]
enabled = true
default_ttl = 86400  # 24 hours
max_size_mb = 1024
compression = true

[cache.strategies]
decomposition_cache = true
api_expansion_cache = true
llm_association_cache = false  # LLM结果不缓存，保持新鲜度

[logging]
# 日志配置
[logging.settings]
level = "INFO"
format = "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
file_enabled = true
console_enabled = true

[logging.files]
main_log = "logs/keyword_architect.log"
api_log = "logs/api_calls.log"
error_log = "logs/errors.log"
performance_log = "logs/performance.log"

[monitoring]
# 监控配置
[monitoring.metrics]
track_api_usage = true
track_processing_time = true
track_error_rates = true
track_cache_hit_rates = true

[monitoring.alerts]
api_quota_threshold = 0.8  # 80% quota usage
error_rate_threshold = 0.1  # 10% error rate
processing_time_threshold = 300  # 5 minutes

[security]
# 安全配置
[security.api_keys]
rotation_enabled = true
encryption_at_rest = false
mask_in_logs = true

[security.data]
sanitize_input = true
validate_output = true
remove_sensitive_data = true

[performance]
# 性能配置
[performance.optimization]
parallel_processing = true
memory_limit_mb = 2048
cpu_limit_percent = 80

[performance.tuning]
io_buffer_size = 8192
network_timeout = 30
connection_pool_size = 10

[deployment]
# 部署配置
[deployment.environment]
type = "standalone"  # standalone, docker, kubernetes
health_check_enabled = true
graceful_shutdown = true

[deployment.resources]
min_memory_mb = 512
recommended_memory_mb = 2048
min_disk_gb = 5
recommended_disk_gb = 20

[deployment.networking]
bind_address = "0.0.0.0"
port = 8080
enable_cors = true
max_connections = 100

[quality_assurance]
# 质量保证配置
[quality_assurance.validation]
seed_word_accuracy_threshold = 0.9
keyword_relevance_threshold = 0.7
duplicate_removal = true
format_validation = true

[quality_assurance.testing]
unit_tests_enabled = true
integration_tests_enabled = true
performance_tests_enabled = true
api_tests_enabled = true

[maintenance]
# 维护配置
[maintenance.schedules]
daily_cleanup = "02:00"
weekly_cache_clear = "Sunday 03:00"
monthly_log_archive = "1st 04:00"

[maintenance.backup]
enabled = true
frequency = "daily"
retention_days = 30
include_cache = false
