# Keyword Architect Agent - Code Structure Blueprint
# 关键词架构师代理 - 代码结构蓝图
# Version: 1.0
# Date: 2025-07-17

## 1. 项目目录结构

```
keyword_architect_agent/
├── main.py                          # 主入口文件
├── config/                          # 配置文件目录
│   ├── __init__.py
│   ├── settings.py                  # 配置加载和管理
│   └── api_config.py               # API配置管理
├── modules/                         # 核心功能模块
│   ├── __init__.py
│   ├── decomposition.py            # 产品拆解模块 (已存在)
│   ├── decomposition_clean.py      # 拆解结果清理 (已存在)
│   ├── api_expansion.py            # API扩展模块 (已存在)
│   ├── api_limiter.py              # API限制管理 (已存在)
│   ├── llm_association.py          # LLM联想模块 (已存在)
│   └── unified_storage.py          # 统一存储管理 (已存在)
├── services/                        # 业务服务层
│   ├── __init__.py
│   ├── data_loader.py              # 数据加载服务
│   ├── keyword_processor.py        # 关键词处理服务
│   ├── result_manager.py           # 结果管理服务
│   └── workflow_orchestrator.py    # 工作流编排服务
├── utils/                           # 工具函数
│   ├── __init__.py
│   ├── file_utils.py               # 文件操作工具
│   ├── validation.py               # 数据验证工具
│   └── logging_config.py           # 日志配置
├── tests/                           # 测试文件
│   ├── __init__.py
│   ├── test_decomposition.py
│   ├── test_api_expansion.py
│   ├── test_llm_association.py
│   └── test_integration.py
├── results/                         # 结果输出目录
├── cache/                           # 缓存目录
├── docs/                           # 文档目录
├── specs/                          # 规约文档
├── prompt_manager/                 # 提示词管理系统 (已存在)
├── knowledge/                      # 知识库 (已存在)
├── source/                         # 源数据 (已存在)
├── google-ads.yaml                 # Google Ads配置 (已存在)
├── deployment.config.toml          # 部署配置
├── requirements.txt                # Python依赖
└── README.md                       # 项目说明
```

## 2. 主入口文件设计

### 2.1 main.py
```python
#!/usr/bin/env python3
"""
Keyword Architect Agent - 主入口文件
关键词架构师代理 - 生物医学产品关键词研究和扩展工具
"""

import sys
import argparse
from pathlib import Path
from typing import Optional

# 添加项目根目录到Python路径
sys.path.insert(0, str(Path(__file__).parent))

from config.settings import load_configuration
from services.workflow_orchestrator import WorkflowOrchestrator
from utils.logging_config import setup_logging


def main():
    """主函数 - 程序入口点"""
    pass


def parse_arguments() -> argparse.Namespace:
    """解析命令行参数"""
    pass


def setup_environment() -> dict:
    """设置运行环境"""
    pass


def run_keyword_processing_workflow(config: dict, input_file: str, output_dir: str) -> bool:
    """运行关键词处理工作流"""
    pass


if __name__ == "__main__":
    main()
```

## 3. 配置管理模块设计

### 3.1 config/settings.py
```python
"""
配置加载和管理模块
"""

import toml
from pathlib import Path
from typing import Dict, Any, Optional
from dataclasses import dataclass


@dataclass
class ProcessingConfig:
    """处理配置"""
    batch_size: int
    max_concurrent: int
    timeout_per_batch: int


@dataclass
class APIConfig:
    """API配置"""
    google_gemini: Dict[str, Any]
    google_keywords_planner: Dict[str, Any]
    rotation: Dict[str, Any]


@dataclass
class StorageConfig:
    """存储配置"""
    directories: Dict[str, str]
    formats: Dict[str, str]
    cleanup: Dict[str, Any]


class ConfigurationManager:
    """配置管理器"""
    
    def __init__(self, config_path: str = "deployment.config.toml"):
        """初始化配置管理器"""
        pass
    
    def load_configuration(self) -> Dict[str, Any]:
        """加载配置文件"""
        pass
    
    def get_processing_config(self) -> ProcessingConfig:
        """获取处理配置"""
        pass
    
    def get_api_config(self) -> APIConfig:
        """获取API配置"""
        pass
    
    def get_storage_config(self) -> StorageConfig:
        """获取存储配置"""
        pass
    
    def validate_configuration(self) -> bool:
        """验证配置有效性"""
        pass


def load_configuration(config_path: str = "deployment.config.toml") -> Dict[str, Any]:
    """加载配置文件（主要函数）"""
    pass
```

### 3.2 config/api_config.py
```python
"""
API配置管理模块
"""

import yaml
from pathlib import Path
from typing import Dict, Any, Optional, List
from dataclasses import dataclass


@dataclass
class GoogleAdsConfig:
    """Google Ads API配置"""
    developer_token: str
    client_id: str
    client_secret: str
    refresh_token: str
    login_customer_id: str
    use_proto_plus: bool


@dataclass
class GeminiAPIConfig:
    """Google Gemini API配置"""
    api_keys: List[str]
    base_url: str
    model: str
    max_tokens: int
    temperature: float
    timeout: int


class APIConfigurationManager:
    """API配置管理器"""
    
    def __init__(self):
        """初始化API配置管理器"""
        pass
    
    def load_google_ads_config(self, config_path: str = "google-ads.yaml") -> GoogleAdsConfig:
        """加载Google Ads配置"""
        pass
    
    def load_gemini_config(self) -> GeminiAPIConfig:
        """加载Gemini API配置"""
        pass
    
    def validate_api_credentials(self) -> Dict[str, bool]:
        """验证API凭证"""
        pass
    
    def get_available_gemini_keys(self) -> List[str]:
        """获取可用的Gemini API密钥"""
        pass


def get_google_ads_config() -> GoogleAdsConfig:
    """获取Google Ads配置（主要函数）"""
    pass


def get_gemini_config() -> GeminiAPIConfig:
    """获取Gemini配置（主要函数）"""
    pass
```

## 4. 业务服务层设计

### 4.1 services/data_loader.py
```python
"""
数据加载服务
"""

import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional, Tuple
from dataclasses import dataclass

from specs.services_interfaces import ProductData, ProcessingResult


@dataclass
class DataValidationResult:
    """数据验证结果"""
    is_valid: bool
    errors: List[str]
    warnings: List[str]
    cleaned_data: Optional[List[ProductData]]


class DataLoaderService:
    """数据加载服务"""
    
    def __init__(self):
        """初始化数据加载服务"""
        pass
    
    def load_product_data(self, file_path: str) -> ProcessingResult:
        """加载产品数据"""
        pass
    
    def validate_product_data(self, products: List[ProductData]) -> DataValidationResult:
        """验证产品数据"""
        pass
    
    def prepare_processing_batches(self, products: List[ProductData], batch_size: int = 10) -> List[List[ProductData]]:
        """准备处理批次"""
        pass
    
    def _detect_file_encoding(self, file_path: str) -> str:
        """检测文件编码"""
        pass
    
    def _clean_product_data(self, df: pd.DataFrame) -> pd.DataFrame:
        """清理产品数据"""
        pass
    
    def _convert_to_product_objects(self, df: pd.DataFrame) -> List[ProductData]:
        """转换为产品对象"""
        pass


def load_product_data(file_path: str) -> ProcessingResult:
    """加载产品数据（主要函数）"""
    pass


def validate_product_data(products: List[ProductData]) -> DataValidationResult:
    """验证产品数据（主要函数）"""
    pass
```

### 4.2 services/keyword_processor.py
```python
"""
关键词处理服务
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from specs.services_interfaces import ProductData, SeedWord, ExpandedKeyword, AssociatedKeyword, ProcessingResult


@dataclass
class KeywordProcessingResult:
    """关键词处理结果"""
    decomposed_keywords: List[SeedWord]
    expanded_keywords: List[ExpandedKeyword]
    associated_keywords: List[AssociatedKeyword]
    processing_stats: Dict[str, Any]
    errors: List[str]


class KeywordProcessorService:
    """关键词处理服务"""
    
    def __init__(self):
        """初始化关键词处理服务"""
        pass
    
    def process_products_batch(self, products: List[ProductData]) -> KeywordProcessingResult:
        """处理产品批次"""
        pass
    
    def decompose_product_names(self, products: List[ProductData]) -> List[SeedWord]:
        """拆解产品名称"""
        pass
    
    def expand_keywords_via_api(self, seed_words: List[str]) -> List[ExpandedKeyword]:
        """通过API扩展关键词"""
        pass
    
    def associate_keywords_via_llm(self, keywords: List[str]) -> List[AssociatedKeyword]:
        """通过LLM联想关键词"""
        pass
    
    def _merge_keyword_results(self, decomposed: List[SeedWord], expanded: List[ExpandedKeyword], 
                              associated: List[AssociatedKeyword]) -> List[Dict[str, Any]]:
        """合并关键词结果"""
        pass
    
    def _calculate_processing_stats(self, result: KeywordProcessingResult) -> Dict[str, Any]:
        """计算处理统计"""
        pass


def process_products_batch(products: List[ProductData]) -> KeywordProcessingResult:
    """处理产品批次（主要函数）"""
    pass
```

### 4.3 services/result_manager.py
```python
"""
结果管理服务
"""

import pandas as pd
from pathlib import Path
from typing import List, Dict, Any, Optional
from dataclasses import dataclass

from specs.services_interfaces import ProcessingResult


@dataclass
class ExportResult:
    """导出结果"""
    success: bool
    file_paths: List[str]
    errors: List[str]
    export_stats: Dict[str, Any]


class ResultManagerService:
    """结果管理服务"""
    
    def __init__(self):
        """初始化结果管理服务"""
        pass
    
    def consolidate_results(self, decomposed: List[Any], expanded: List[Any], 
                           associated: List[Any]) -> pd.DataFrame:
        """合并所有结果"""
        pass
    
    def export_results(self, data: pd.DataFrame, output_dir: str, 
                      formats: List[str] = ["csv"]) -> ExportResult:
        """导出结果"""
        pass
    
    def generate_summary_report(self, data: pd.DataFrame) -> Dict[str, Any]:
        """生成汇总报告"""
        pass
    
    def save_processing_metadata(self, metadata: Dict[str, Any], output_dir: str) -> str:
        """保存处理元数据"""
        pass
    
    def _format_csv_output(self, data: pd.DataFrame) -> pd.DataFrame:
        """格式化CSV输出"""
        pass
    
    def _format_excel_output(self, data: pd.DataFrame) -> pd.DataFrame:
        """格式化Excel输出"""
        pass
    
    def _generate_filename(self, base_name: str, format_type: str) -> str:
        """生成文件名"""
        pass


def consolidate_results(decomposed: List[Any], expanded: List[Any], 
                       associated: List[Any]) -> pd.DataFrame:
    """合并结果（主要函数）"""
    pass


def export_results(data: pd.DataFrame, output_dir: str, 
                  formats: List[str] = ["csv"]) -> ExportResult:
    """导出结果（主要函数）"""
    pass
```

### 4.4 services/workflow_orchestrator.py
```python
"""
工作流编排服务
"""

from typing import List, Dict, Any, Optional
from dataclasses import dataclass
from pathlib import Path

from services.data_loader import DataLoaderService
from services.keyword_processor import KeywordProcessorService
from services.result_manager import ResultManagerService


@dataclass
class WorkflowResult:
    """工作流结果"""
    success: bool
    output_files: List[str]
    processing_stats: Dict[str, Any]
    errors: List[str]
    warnings: List[str]


class WorkflowOrchestrator:
    """工作流编排器"""
    
    def __init__(self, config: Dict[str, Any]):
        """初始化工作流编排器"""
        pass
    
    def run_complete_workflow(self, input_file: str, output_dir: str) -> WorkflowResult:
        """运行完整工作流"""
        pass
    
    def run_decomposition_only(self, input_file: str, output_dir: str) -> WorkflowResult:
        """仅运行拆解流程"""
        pass
    
    def run_expansion_only(self, seed_words_file: str, output_dir: str) -> WorkflowResult:
        """仅运行扩展流程"""
        pass
    
    def run_association_only(self, keywords_file: str, output_dir: str) -> WorkflowResult:
        """仅运行联想流程"""
        pass
    
    def _setup_output_directory(self, output_dir: str) -> None:
        """设置输出目录"""
        pass
    
    def _log_workflow_progress(self, stage: str, progress: float, message: str) -> None:
        """记录工作流进度"""
        pass
    
    def _handle_workflow_error(self, error: Exception, stage: str) -> None:
        """处理工作流错误"""
        pass


def run_complete_workflow(input_file: str, output_dir: str, config: Dict[str, Any]) -> WorkflowResult:
    """运行完整工作流（主要函数）"""
    pass
```

## 5. 工具函数设计

### 5.1 utils/file_utils.py
```python
"""
文件操作工具
"""

import shutil
from pathlib import Path
from typing import List, Optional, Dict, Any


def ensure_directory_exists(directory: str) -> None:
    """确保目录存在"""
    pass


def cleanup_temp_files(temp_dir: str, max_age_hours: int = 24) -> int:
    """清理临时文件"""
    pass


def get_file_size_mb(file_path: str) -> float:
    """获取文件大小（MB）"""
    pass


def backup_file(file_path: str, backup_dir: str) -> str:
    """备份文件"""
    pass


def compress_file(file_path: str, compression_type: str = "gzip") -> str:
    """压缩文件"""
    pass


def get_directory_size(directory: str) -> Dict[str, Any]:
    """获取目录大小统计"""
    pass
```

### 5.2 utils/validation.py
```python
"""
数据验证工具
"""

import re
from typing import List, Dict, Any, Optional, Tuple


def validate_email(email: str) -> bool:
    """验证邮箱格式"""
    pass


def validate_product_name(name: str) -> Tuple[bool, List[str]]:
    """验证产品名称"""
    pass


def validate_keyword(keyword: str) -> Tuple[bool, List[str]]:
    """验证关键词"""
    pass


def sanitize_filename(filename: str) -> str:
    """清理文件名"""
    pass


def validate_api_response(response: Dict[str, Any], expected_fields: List[str]) -> Tuple[bool, List[str]]:
    """验证API响应"""
    pass


def check_data_quality(data: List[Dict[str, Any]]) -> Dict[str, Any]:
    """检查数据质量"""
    pass
```

### 5.3 utils/logging_config.py
```python
"""
日志配置工具
"""

import logging
import logging.handlers
from pathlib import Path
from typing import Dict, Any


def setup_logging(config: Dict[str, Any]) -> None:
    """设置日志配置"""
    pass


def get_logger(name: str) -> logging.Logger:
    """获取日志记录器"""
    pass


def log_api_call(api_name: str, success: bool, response_time: float, details: Dict[str, Any]) -> None:
    """记录API调用"""
    pass


def log_processing_stats(stage: str, stats: Dict[str, Any]) -> None:
    """记录处理统计"""
    pass


def log_error_with_context(error: Exception, context: Dict[str, Any]) -> None:
    """记录带上下文的错误"""
    pass
```

## 6. 模块依赖关系

```
main.py
├── config/settings.py
├── config/api_config.py
├── services/workflow_orchestrator.py
│   ├── services/data_loader.py
│   ├── services/keyword_processor.py
│   │   ├── modules/decomposition.py
│   │   ├── modules/api_expansion.py
│   │   ├── modules/llm_association.py
│   │   └── modules/api_limiter.py
│   └── services/result_manager.py
│       └── modules/unified_storage.py
├── utils/logging_config.py
├── utils/file_utils.py
└── utils/validation.py
```

## 7. 实现优先级

### 优先级1（核心功能）
1. `config/settings.py` - 配置管理
2. `services/data_loader.py` - 数据加载
3. `services/workflow_orchestrator.py` - 工作流编排
4. `main.py` - 主入口

### 优先级2（业务逻辑）
1. `services/keyword_processor.py` - 关键词处理
2. `services/result_manager.py` - 结果管理
3. `config/api_config.py` - API配置

### 优先级3（支持功能）
1. `utils/logging_config.py` - 日志配置
2. `utils/validation.py` - 数据验证
3. `utils/file_utils.py` - 文件工具

### 优先级4（测试和文档）
1. 单元测试文件
2. 集成测试文件
3. README.md更新
