# Keyword Architect Agent - High Level Architecture
# 关键词架构师代理 - 高层架构设计
# Version: 1.0
# Date: 2025-07-17

## 1. 系统架构概览

### 1.1 架构原则
- **函数式设计**: 每个核心功能实现为独立函数，避免过度封装
- **单一职责**: 每个模块只负责一个明确的功能领域
- **缓存优先**: 优先使用缓存数据，减少API调用
- **错误处理约束**: 仅在外部API调用时使用try-except，其他地方使用返回值模式
- **API轮换机制**: Gemini API支持自动轮换，配额耗尽时自动切换
- **配额管理**: 严格管理API调用限制

### 1.2 系统组件图
```
┌─────────────────────────────────────────────────────────────────┐
│                    Keyword Architect Agent                      │
├─────────────────────────────────────────────────────────────────┤
│  Data Input Layer                                               │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │ CSV Data Loader │    │ Data Validator  │                    │
│  └─────────────────┘    └─────────────────┘                    │
├─────────────────────────────────────────────────────────────────┤
│  Core Processing Layer                                          │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │ Decomposition   │ │ API Expansion   │ │ LLM Association │   │
│  │ Module          │ │ Module          │ │ Module          │   │
│  │ (AutoGen v0.4)  │ │ (Google KP API) │ │ (AutoGen v0.4)  │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
├─────────────────────────────────────────────────────────────────┤
│  Support Services Layer                                         │
│  ┌─────────────────┐ ┌─────────────────┐ ┌─────────────────┐   │
│  │ API Limiter     │ │ Cache Manager   │ │ Prompt Manager  │   │
│  │ (Quota Control) │ │ (File Cache)    │ │ (System)        │   │
│  └─────────────────┘ └─────────────────┘ └─────────────────┘   │
├─────────────────────────────────────────────────────────────────┤
│  Data Output Layer                                              │
│  ┌─────────────────┐    ┌─────────────────┐                    │
│  │ Unified Storage │    │ Result Manager  │                    │
│  │ Manager         │    │ (CSV/JSON)      │                    │
│  └─────────────────┘    └─────────────────┘                    │
└─────────────────────────────────────────────────────────────────┘
```

## 2. 核心模块设计

### 2.1 产品拆解模块 (Decomposition Module)
**位置**: `modules/decomposition.py`
**技术栈**: AutoGen v0.4 + Google Gemini 2.5 Flash + prompt_manager
**职责**:
- 加载CSV产品数据
- 使用LLM将产品名拆解为种子词
- 验证和清理拆解结果
- 保存拆解结果到统一存储

**关键设计模式**:
- 使用AutoGen v0.4的AssistantAgent进行LLM交互
- 使用prompt_manager管理提示词模板
- 批量处理产品数据（每批10个）
- Gemini API自动轮换机制（2个API密钥）
- 仅在API调用时使用try-except错误处理

### 2.2 API扩展模块 (API Expansion Module)
**位置**: `modules/api_expansion.py`
**技术栈**: Google Keywords Planner API + 缓存系统
**职责**:
- 基于种子词调用Google Keywords Planner API
- 严格管理API调用限制（10次/小时）
- 实现智能缓存策略
- 支持多API密钥轮换

**关键设计模式**:
- 缓存优先策略：先检查缓存，再调用API
- 配额管理：严格控制API调用次数
- 错误处理：记录失败调用，支持降级策略
- 数据结构：使用dataclass定义ExpandedKeyword

### 2.3 LLM联想模块 (LLM Association Module)
**位置**: `modules/llm_association.py`
**技术栈**: AutoGen v0.4 + Google Gemini 2.5 Flash
**职责**:
- 基于关键词进行语义和场景联想
- 支持多种联想场景（临床使用、研究应用、商业搜索）
- 生成深层次的用户查询关键词

**关键设计模式**:
- 场景化联想：针对不同使用场景生成不同类型的关键词
- 异步处理：使用asyncio处理LLM调用
- API轮换：Gemini API配额耗尽时自动切换到备用API
- 结果验证：验证联想结果的相关性和质量

## 3. 支持服务设计

### 3.1 API限制管理器 (API Limiter)
**位置**: `modules/api_limiter.py`
**职责**:
- 管理Google Keywords Planner API的10次/小时限制
- 记录详细的API调用历史
- 支持多API密钥自动轮换
- 提供配额状态查询

**核心功能**:
```python
class APICallLimiter:
    - check_quota_status() -> QuotaStatus
    - record_api_call() -> None
    - get_available_api_key() -> Optional[str]
    - switch_to_next_api() -> bool
```

### 3.2 统一存储管理器 (Unified Storage Manager)
**位置**: `modules/unified_storage.py`
**职责**:
- 统一管理所有结果文件到results/目录
- 支持多种数据格式（CSV、JSON、文本）
- 自动清理临时文件
- 提供数据加载和保存接口

**存储结构**:
```
results/
├── decomposition_results.json    # 拆解结果
├── expansion_results.json        # API扩展结果
├── association_results.json      # LLM联想结果
├── keyword_data.csv              # 最终统一结果
└── api_call_history.json         # API调用历史
```

## 4. 数据流设计

### 4.1 主要数据流
```
CSV产品数据 → 数据验证 → 产品拆解 → 种子词提取
     ↓
种子词列表 → 缓存检查 → API扩展 → 扩展关键词
     ↓
扩展关键词 → LLM联想 → 场景化关键词 → 结果合并
     ↓
统一关键词数据 → 格式化 → CSV导出 → 最终结果
```

### 4.2 错误处理流
```
API调用失败 → 记录错误 → 检查缓存 → 降级策略
     ↓
LLM调用失败 → 重试机制 → 跳过处理 → 继续流程
     ↓
数据验证失败 → 记录警告 → 清理数据 → 继续处理
```

## 5. 配置管理

### 5.1 API配置
- Google Gemini API: 环境变量 + 多密钥轮换
- Google Ads API: `google-ads.yaml` 配置文件
- API限制: 可配置的调用频率控制

### 5.2 处理配置
- 批量大小: 默认10个产品/批次
- 超时设置: API调用60秒超时
- 重试次数: 失败后最多重试3次
- 缓存策略: 文件缓存，永久有效

## 6. 质量保证

### 6.1 数据质量控制
- 种子词提取准确率 > 90%
- 关键词相关性评分 > 0.7
- 重复关键词自动去除
- 数据格式验证和清理

### 6.2 性能监控
- API调用响应时间监控
- 处理进度实时显示
- 错误率统计和报告
- 缓存命中率监控

## 7. 扩展性设计

### 7.1 模块化扩展
- 新的LLM模型：通过AutoGen v0.4轻松集成
- 新的API源：通过统一接口扩展
- 新的数据格式：通过存储管理器扩展

### 7.2 配置化扩展
- 联想场景可配置
- API限制可调整
- 处理参数可定制
