# Keyword Architect Agent - Technical Specification
# 关键词架构师代理 - 技术规约
# Version: 1.0
# Date: 2025-07-17

## 1. 系统概述

### 1.1 系统目标
为生物医学产品（如ELISA试剂盒、过敏测试）提供全面的关键词研究和扩展服务，通过三阶段处理流程生成结构化的关键词策略。

### 1.2 核心功能模块
- **原子化拆解模块**: 使用Google Gemini 2.5 Flash将产品名拆解为种子词
- **API驱动扩展模块**: 调用Google Keywords Planner API获取关键词扩展
- **LLM驱动联想模块**: 使用AI进行深层语义和场景联想
- **结果管理模块**: 统一存储和管理生成的关键词数据

## 2. 功能规约 (Gherkin Scenarios)

### 2.1 产品原子化拆解

```gherkin
Feature: 产品名称原子化拆解
  作为营销人员
  我希望将产品名称拆解为种子词
  以便获得基础的关键词组件

  Background:
    Given 系统已加载source/backed.csv产品数据
    And Google Gemini 2.5 Flash API已配置
    And prompt_manager系统已初始化

  Scenario: 成功拆解单个产品名称
    Given 产品名称为"Human IL-6 ELISA Kit"
    When 调用原子化拆解功能
    Then 应该返回种子词列表包含["ELISA", "IL-6", "Human", "Kit", "test"]
    And 拆解结果应该保存到decomposition_results.json
    And API调用记录应该更新

  Scenario: 批量处理产品数据
    Given CSV文件包含多个产品记录
    When 执行批量拆解处理
    Then 每个产品都应该生成对应的种子词
    And 处理进度应该可追踪
    And 失败的记录应该记录错误信息

  Scenario: 处理异常产品名称
    Given 产品名称包含特殊字符或格式异常
    When 调用原子化拆解功能
    Then 系统应该优雅处理异常
    And 返回可用的种子词或错误信息
    And 不应该中断整个处理流程
```

### 2.2 API驱动关键词扩展

```gherkin
Feature: API驱动关键词扩展
  作为营销人员
  我希望基于种子词获取大量相关关键词
  以便发现更多潜在的搜索词汇

  Background:
    Given Google Keywords Planner API已配置
    And API调用限制已设置
    And 缓存机制已启用

  Scenario: 基于种子词扩展关键词
    Given 种子词为"ELISA"
    When 调用Google Keywords Planner API
    Then 应该返回相关关键词列表
    And 包含搜索量和竞争度数据
    And API调用记录应该更新
    And 结果应该缓存24小时

  Scenario: API调用限制保护
    Given API调用次数接近限制
    When 尝试进行关键词扩展
    Then 系统应该检查调用历史
    And 如果超出限制则使用缓存数据
    And 记录限制触发事件

  Scenario: API调用失败处理
    Given API服务不可用或返回错误
    When 执行关键词扩展
    Then 系统应该重试指定次数
    And 记录失败原因
    And 返回部分结果或错误状态
```

### 2.3 LLM驱动语义联想

```gherkin
Feature: LLM驱动语义联想
  作为营销人员
  我希望通过AI进行深层语义和场景联想
  以便发现API无法覆盖的用户查询模式

  Background:
    Given Google Gemini 2.5 Flash已配置
    And AutoGen v0.4框架已初始化
    And 联想场景已定义

  Scenario: 生成语义关联关键词
    Given 基础关键词为"IL-6 test"
    When 执行语义联想分析
    Then 应该生成临床使用场景关键词
    And 应该生成研究应用场景关键词
    And 应该生成商业搜索场景关键词
    And 结果应该以表格形式输出

  Scenario: 多层次联想扩展
    Given 设置联想深度为3级
    When 执行深度语义联想
    Then 应该生成第一级直接关联词
    And 应该生成第二级间接关联词
    And 应该生成第三级场景关联词
    And 每级关联都应该有明确标识

  Scenario: 联想结果质量控制
    Given 生成的联想关键词
    When 进行质量评估
    Then 应该过滤重复关键词
    And 应该验证关键词相关性
    And 应该限制每个关键词的联想数量
```

### 2.4 结果统一管理

```gherkin
Feature: 结果统一存储管理
  作为系统管理员
  我希望所有生成的关键词数据统一存储
  以便查找、管理和后续分析

  Background:
    Given 存储目录结构已创建
    And 数据格式标准已定义

  Scenario: 保存处理结果
    Given 完成了完整的关键词处理流程
    When 保存最终结果
    Then 应该生成统一的keyword_data.csv文件
    And 应该包含所有阶段的关键词数据
    And 应该标识关键词来源和类型
    And 文件应该易于Excel打开和分析

  Scenario: API调用历史记录
    Given 系统进行了多次API调用
    When 保存调用记录
    Then 应该记录每次调用的时间戳
    And 应该记录调用的API类型和参数
    And 应该记录返回的数据量
    And 应该记录调用成功/失败状态

  Scenario: 数据检索和导出
    Given 系统中存储了历史关键词数据
    When 用户请求数据检索
    Then 应该支持按产品名称检索
    And 应该支持按关键词类型过滤
    And 应该支持数据导出为多种格式
```

## 3. 数据模型规约

### 3.1 输入数据结构
```yaml
ProductData:
  product_idex: string
  product_sku_no: string
  product_unified_name: string
  product_category: string
  product_seo_keywords: string (existing)
  product_seo_description: string
```

### 3.2 种子词数据结构
```yaml
SeedWord:
  product_id: string
  original_name: string
  seed_words: list[string]
  extraction_method: string
  confidence_score: float
  timestamp: datetime
```

### 3.3 扩展关键词数据结构
```yaml
ExpandedKeyword:
  seed_word: string
  keyword: string
  search_volume: integer
  competition: string
  cpc: float
  source: string ("google_ads")
  timestamp: datetime
```

### 3.4 联想关键词数据结构
```yaml
AssociatedKeyword:
  base_keyword: string
  associated_keyword: string
  association_type: string
  scenario: string
  relevance_score: float
  source: string ("gemini_llm")
  timestamp: datetime
```

## 4. API接口规约

### 4.1 核心处理接口
```python
def decompose_product_names(products: List[ProductData]) -> List[SeedWord]
def expand_keywords_via_api(seed_words: List[str]) -> List[ExpandedKeyword]
def associate_keywords_via_llm(keywords: List[str]) -> List[AssociatedKeyword]
def consolidate_results(decomposed, expanded, associated) -> DataFrame
```

### 4.2 数据管理接口
```python
def save_results(data: DataFrame, format: str = "csv") -> str
def load_api_call_history() -> List[APICall]
def save_api_call_record(call: APICall) -> None
def get_cached_results(cache_key: str) -> Optional[Any]
```

## 5. 性能和约束规约

### 5.1 API调用限制
- Google Keywords Planner API: 最多10次/小时
- Google Gemini API: 合理使用，避免过度调用
- 实现调用记录和缓存机制

### 5.2 处理性能
- 批量处理: 每批10个产品
- 超时设置: 单次API调用60秒
- 重试机制: 失败后最多重试3次

### 5.3 数据质量
- 种子词提取准确率 > 90%
- 关键词相关性评分 > 0.7
- 重复关键词自动去除

## 6. 错误处理规约

### 6.1 API错误处理
- 网络超时: 自动重试
- API限制: 使用缓存数据
- 认证失败: 记录错误并停止处理

### 6.2 数据错误处理
- 格式错误: 跳过并记录
- 缺失数据: 使用默认值或跳过
- 编码问题: 自动检测和转换

## 7. 安全和合规规约

### 7.1 API密钥管理
- 使用环境变量存储API密钥
- 不在代码中硬编码敏感信息
- 实现密钥轮换机制（如需要）

### 7.2 数据隐私
- 不记录敏感的产品信息
- API调用日志脱敏处理
- 遵循数据保留政策
