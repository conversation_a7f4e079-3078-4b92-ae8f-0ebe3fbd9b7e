# Keyword Architect Agent - Implementation Patterns
# 关键词架构师代理 - 实现模式文档
# Version: 1.0
# Date: 2025-07-17
# 基于现有代码分析的实现模式

## 1. 错误处理模式 (Error Handling Patterns)

### 1.1 API调用错误处理模式（仅限API调用）
**模式来源**: `modules/api_expansion.py`
**适用范围**: 仅限于大模型API和Google Keyword Planner API调用

```python
# API调用错误处理模式（仅限外部API）
def call_google_keyword_planner_api(seed_word: str) -> List[ExpandedKeyword]:
    """Google Keyword Planner API调用（允许使用try-except）"""
    try:
        start_time = time.time()
        # Google Ads API调用逻辑
        result = google_ads_client.search_keywords(seed_word)
        response_time = time.time() - start_time

        # 记录成功调用
        record_api_call(seed_word, True, len(result), None, response_time)
        return result

    except GoogleAdsException as ex:
        response_time = time.time() - start_time
        error_msg = f"Google Ads API错误: {ex.error.message if ex.error else str(ex)}"

        # 记录失败调用
        record_api_call(seed_word, False, 0, error_msg, response_time)
        print(f"[ERROR] {error_msg}")
        raise

    except Exception as e:
        response_time = time.time() - start_time
        error_msg = f"API调用异常: {str(e)}"

        # 记录失败调用
        record_api_call(seed_word, False, 0, error_msg, response_time)
        print(f"[ERROR] {error_msg}")
        raise

def call_gemini_api_with_rotation(prompt: str) -> str:
    """Gemini API调用（允许使用try-except，支持API轮换）"""
    api_keys = ["AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo", "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E"]

    for api_key in api_keys:
        try:
            # 使用当前API密钥调用Gemini
            client = OpenAIChatCompletionClient(
                model="gemini-2.5-flash",
                api_key=api_key,
                base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
            )

            response = client.complete(prompt)
            print(f"[GEMINI_API] 成功使用API密钥: {api_key[:20]}...")
            return response

        except Exception as e:
            print(f"[GEMINI_API] API密钥 {api_key[:20]}... 调用失败: {str(e)}")
            # 如果不是最后一个API密钥，继续尝试下一个
            if api_key != api_keys[-1]:
                print(f"[GEMINI_API] 切换到下一个API密钥...")
                continue
            else:
                # 所有API密钥都失败，抛出异常
                raise Exception(f"所有Gemini API密钥都不可用: {str(e)}")
```

### 1.2 非API调用错误处理模式（禁用try-except）
**约束**: 除API调用外，其他所有代码禁止使用try-except

```python
# 数据验证模式（不使用try-except）
def validate_product_data(data: pd.DataFrame) -> Tuple[bool, List[str]]:
    """数据验证（不使用try-except）"""
    errors = []

    # 检查必需列
    required_columns = ['product_unified_name', 'product_category']
    for col in required_columns:
        if col not in data.columns:
            errors.append(f"缺少必需列: {col}")

    # 检查数据类型
    if not isinstance(data, pd.DataFrame):
        errors.append("输入数据必须是pandas DataFrame")

    # 检查数据为空
    if len(data) == 0:
        errors.append("数据为空")

    is_valid = len(errors) == 0
    return is_valid, errors

# 文件操作模式（不使用try-except）
def save_data_safely(data: Any, file_path: str) -> Tuple[bool, str]:
    """安全保存数据（不使用try-except）"""
    # 预检查
    if data is None:
        return False, "数据为空"

    if not file_path:
        return False, "文件路径为空"

    # 检查目录是否存在
    directory = Path(file_path).parent
    if not directory.exists():
        directory.mkdir(parents=True, exist_ok=True)

    # 根据数据类型保存
    if isinstance(data, pd.DataFrame):
        data.to_csv(file_path, index=False, encoding='utf-8')
    elif isinstance(data, (dict, list)):
        with open(file_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
    else:
        return False, f"不支持的数据类型: {type(data)}"

    # 验证保存结果
    if Path(file_path).exists() and Path(file_path).stat().st_size > 0:
        return True, f"数据已保存到: {file_path}"
    else:
        return False, "文件保存失败"
```

**关键约束**:
- try-except仅限于外部API调用
- 其他所有代码使用返回值模式处理错误
- 预检查和验证优于异常处理
- 明确的成功/失败返回值

## 2. 缓存管理模式 (Cache Management Patterns)

### 2.1 文件缓存模式
**模式来源**: `modules/api_expansion.py`

```python
# 缓存检查和保存模式
def _load_from_cache(self, seed_word: str) -> Optional[List[ExpandedKeyword]]:
    """从缓存加载扩展关键词"""
    cache_file = self.cache_dir / f"expansion_{seed_word.lower().replace(' ', '_')}.json"
    
    if not cache_file.exists():
        return None
    
    try:
        with open(cache_file, 'r', encoding='utf-8') as f:
            cached_data = json.load(f)
        
        # 转换为ExpandedKeyword对象
        keywords = [ExpandedKeyword(**item) for item in cached_data]
        print(f"[CACHE] 从缓存加载: {seed_word} -> {len(keywords)} 个关键词")
        return keywords
        
    except Exception as e:
        print(f"[CACHE] 缓存加载失败: {e}")
        return None

def _save_to_cache(self, seed_word: str, keywords: List[ExpandedKeyword]) -> None:
    """保存扩展关键词到缓存"""
    cache_file = self.cache_dir / f"expansion_{seed_word.lower().replace(' ', '_')}.json"
    
    try:
        # 转换为可序列化的字典
        cache_data = [asdict(keyword) for keyword in keywords]
        
        with open(cache_file, 'w', encoding='utf-8') as f:
            json.dump(cache_data, f, indent=2, ensure_ascii=False)
        
        print(f"[CACHE] 保存到缓存: {seed_word} -> {len(keywords)} 个关键词")
        
    except Exception as e:
        print(f"[CACHE] 缓存保存失败: {e}")
```

**关键特征**:
- 文件名标准化（小写、下划线替换空格）
- JSON格式存储，支持中文
- 对象与字典的转换处理
- 缓存操作的详细日志

### 2.2 缓存优先策略
**模式来源**: `modules/api_expansion.py`

```python
# 缓存优先处理流程
def expand_single_seed_word(self, seed_word: str) -> List[ExpandedKeyword]:
    # 1. 首先检查缓存
    cached_keywords = self._load_from_cache(seed_word)
    if cached_keywords:
        return cached_keywords
    
    # 2. 检查API配额
    if not check_api_quota():
        quota_status = api_limiter.check_quota_status()
        print(f"[API_EXPANSION] API配额已用完，下次可用时间: {quota_status.next_available_time}")
        return []
    
    # 3. 调用API
    try:
        keywords = self.call_keywords_planner_api(seed_word)
        # 4. 保存到缓存
        self._save_to_cache(seed_word, keywords)
        return keywords
    except Exception as e:
        print(f"[ERROR] 种子词 {seed_word} 扩展失败: {e}")
        return []
```

## 3. API配额管理模式 (API Quota Management Patterns)

### 3.1 配额检查模式
**模式来源**: `modules/api_limiter.py`

```python
# 配额状态检查模式
def check_quota_status(self) -> QuotaStatus:
    """检查当前API配额状态"""
    history = self._load_history()
    used_quota = self._count_current_hour_calls(history, self.current_api_key_id)
    
    remaining = max(0, self.max_calls_per_hour - used_quota)
    
    # 计算下次可用时间
    if remaining == 0:
        # 找到最早的调用时间，加1小时
        current_hour_calls = [
            call for call in history 
            if call.api_key_id == self.current_api_key_id and 
            self._is_within_current_hour(call.timestamp)
        ]
        if current_hour_calls:
            earliest_call = min(current_hour_calls, key=lambda x: x.timestamp)
            next_available = datetime.fromisoformat(earliest_call.timestamp) + timedelta(hours=1)
        else:
            next_available = datetime.now()
    else:
        next_available = None
    
    return QuotaStatus(
        used_quota=used_quota,
        remaining_quota=remaining,
        max_quota=self.max_calls_per_hour,
        current_api_key=self.current_api_key_id,
        next_available_time=next_available.isoformat() if next_available else None
    )
```

### 3.2 API轮换模式
**模式来源**: `modules/api_limiter.py`

```python
# API密钥轮换模式
def get_available_api_key(self) -> Optional[str]:
    """获取可用的API密钥ID（支持自动轮换）"""
    history = self._load_history()
    
    # 检查所有API密钥的可用性
    for api_key_id in self.api_keys.keys():
        used_quota = self._count_current_hour_calls(history, api_key_id)
        if used_quota < self.max_calls_per_hour:
            return api_key_id
    
    return None

def switch_to_next_api(self) -> bool:
    """切换到下一个可用的API"""
    available_api = self.get_available_api_key()
    if available_api and available_api != self.current_api_key_id:
        old_api = self.current_api_key_id
        self.current_api_key_id = available_api
        print(f"[API_LIMITER] API切换: {old_api} -> {self.current_api_key_id}")
        return True
    return False
```

## 4. 数据结构模式 (Data Structure Patterns)

### 4.1 数据类定义模式
**模式来源**: 各模块的数据类定义

```python
# 标准数据类模式
@dataclass
class ExpandedKeyword:
    """扩展关键词数据结构"""
    seed_word: str
    keyword: str
    search_volume: Optional[int]
    competition: Optional[str]
    cpc: Optional[float]
    source: str
    timestamp: str

@dataclass
class APICallRecord:
    """API调用记录数据结构"""
    timestamp: str
    seed_word: str
    success: bool
    result_count: int
    api_key_id: str
    error_message: Optional[str] = None
    response_time: Optional[float] = None
```

**关键特征**:
- 使用@dataclass装饰器
- 明确的类型注解
- Optional类型用于可选字段
- 包含时间戳和来源信息

### 4.2 数据转换模式
**模式来源**: `modules/unified_storage.py`

```python
# 数据序列化/反序列化模式
def save_to_unified_storage(self, data: Any, data_type: str, filename: str) -> str:
    if data_type == "json":
        with open(file_path, 'w', encoding='utf-8') as f:
            if hasattr(data, '__dict__') or isinstance(data, dict):
                json.dump(data if isinstance(data, dict) else asdict(data), 
                        f, indent=2, ensure_ascii=False, default=str)
            else:
                json.dump(data, f, indent=2, ensure_ascii=False, default=str)
```

## 5. AutoGen v0.4 集成模式

### 5.1 Agent初始化模式（支持API轮换）
**模式来源**: `modules/decomposition.py`, `modules/llm_association.py`

```python
# AutoGen Agent初始化模式（支持Gemini API轮换）
def initialize_autogen_agent_with_rotation(self) -> Tuple[bool, str]:
    """初始化AutoGen v0.4代理（支持API轮换，不使用try-except）"""

    # Gemini API密钥列表
    api_keys = ["AIzaSyBWPiNDni24gEzdZUzNUhVxws6prDHB4Yo", "AIzaSyBxVGph11TTLKKfGBXH3GVPIXriYaroV9E"]

    for i, api_key in enumerate(api_keys):
        # 创建模型客户端
        model_client = OpenAIChatCompletionClient(
            model="gemini-2.5-flash",
            api_key=api_key,
            base_url="https://generativelanguage.googleapis.com/v1beta/openai/"
        )

        # 测试API密钥是否可用
        test_success, test_message = test_gemini_api_key(model_client)

        if test_success:
            # 创建AssistantAgent
            self.agent = AssistantAgent(
                name="keyword_processor",
                model_client=model_client,
                system_message="你是一个专业的生物医学关键词处理专家。"
            )

            self.current_api_key = api_key
            print(f"[AUTOGEN] 成功初始化，使用API密钥: {api_key[:20]}...")
            return True, "AutoGen代理初始化成功"
        else:
            print(f"[AUTOGEN] API密钥 {api_key[:20]}... 不可用: {test_message}")
            if i < len(api_keys) - 1:
                print(f"[AUTOGEN] 尝试下一个API密钥...")
                continue

    # 所有API密钥都不可用
    return False, "所有Gemini API密钥都不可用"

def test_gemini_api_key(model_client) -> Tuple[bool, str]:
    """测试Gemini API密钥是否可用（允许使用try-except）"""
    try:
        # 发送简单的测试请求
        test_message = TextMessage(content="test", source="user")
        response = model_client.complete([test_message])
        return True, "API密钥可用"
    except Exception as e:
        return False, str(e)
```

### 5.2 异步LLM调用模式（支持API轮换）
**模式来源**: `modules/llm_association.py`

```python
# 异步LLM调用模式（支持API轮换）
def call_llm_with_rotation(self, prompt: str) -> Tuple[bool, str, str]:
    """调用LLM（支持API轮换，不使用try-except）"""

    # 如果当前agent不可用，重新初始化
    if not hasattr(self, 'agent') or self.agent is None:
        init_success, init_message = self.initialize_autogen_agent_with_rotation()
        if not init_success:
            return False, "", init_message

    # 尝试调用当前agent
    call_success, response, error_msg = self.call_current_agent(prompt)

    if call_success:
        return True, response, ""

    # 当前API失败，尝试切换
    print(f"[LLM] 当前API调用失败: {error_msg}")
    print(f"[LLM] 尝试切换API密钥...")

    # 重新初始化agent（会自动尝试下一个可用的API）
    init_success, init_message = self.initialize_autogen_agent_with_rotation()
    if not init_success:
        return False, "", f"API切换失败: {init_message}"

    # 使用新的API重试
    retry_success, retry_response, retry_error = self.call_current_agent(prompt)

    if retry_success:
        print(f"[LLM] API切换成功，重试调用成功")
        return True, retry_response, ""
    else:
        return False, "", f"重试失败: {retry_error}"

def call_current_agent(self, prompt: str) -> Tuple[bool, str, str]:
    """调用当前agent（允许使用try-except）"""
    try:
        import asyncio

        async def get_response():
            message = TextMessage(content=prompt, source="user")
            response = await self.agent.on_messages([message], cancellation_token=None)
            return response.content if hasattr(response, 'content') else str(response)

        # 运行异步函数
        loop = asyncio.new_event_loop()
        asyncio.set_event_loop(loop)
        try:
            response = loop.run_until_complete(get_response())
            return True, response, ""
        finally:
            loop.close()

    except Exception as e:
        return False, "", str(e)
```

## 6. 日志和监控模式

### 6.1 统一日志格式
**模式来源**: 所有模块

```python
# 统一日志格式模式
print(f"[MODULE_NAME] 操作描述: 详细信息")
print(f"[ERROR] 错误描述: {error_details}")
print(f"[WARNING] 警告信息")
print(f"[CACHE] 缓存操作: 操作结果")
print(f"[API_EXPANSION] API操作: 操作状态")
```

**关键特征**:
- 使用方括号标识模块名
- 统一的错误、警告标识
- 包含操作结果的详细信息
- 支持中文日志信息
