# Keyword Architect Agent - Services Interfaces Specification
# 关键词架构师代理 - 服务接口规约
# Version: 1.0
# Date: 2025-07-17

## 1. 接口设计原则

### 1.1 设计原则
- **函数式设计**: 每个核心功能实现为独立函数
- **单一职责**: 每个接口只负责一个明确的功能
- **错误透明**: 所有错误都应该被捕获和适当处理
- **数据验证**: 所有输入都必须经过验证
- **缓存友好**: 支持结果缓存以提高性能

### 1.2 通用数据类型
```python
from typing import List, Dict, Optional, Union
from dataclasses import dataclass
from datetime import datetime

@dataclass
class ProductData:
    product_idex: str
    product_sku_no: str
    product_unified_name: str
    product_category: str
    product_seo_keywords: Optional[str] = None
    product_seo_description: Optional[str] = None

@dataclass
class SeedWord:
    product_id: str
    original_name: str
    seed_words: List[str]
    extraction_method: str
    confidence_score: float
    timestamp: datetime

@dataclass
class ExpandedKeyword:
    seed_word: str
    keyword: str
    search_volume: Optional[int]
    competition: Optional[str]
    cpc: Optional[float]
    source: str
    timestamp: datetime

@dataclass
class AssociatedKeyword:
    base_keyword: str
    associated_keyword: str
    association_type: str
    scenario: str
    relevance_score: float
    source: str
    timestamp: datetime

@dataclass
class ProcessingResult:
    success: bool
    data: Optional[Union[List, Dict]]
    error_message: Optional[str]
    processing_time: float
    metadata: Dict
```

## 2. 核心服务接口

### 2.1 产品数据加载服务

```python
def load_product_data(file_path: str) -> ProcessingResult:
    """
    加载和验证CSV产品数据
    
    Args:
        file_path: CSV文件路径
        
    Returns:
        ProcessingResult: 包含产品数据列表或错误信息
        
    Raises:
        FileNotFoundError: 文件不存在
        ValueError: 数据格式错误
    """
    pass

def validate_product_data(products: List[ProductData]) -> ProcessingResult:
    """
    验证产品数据的完整性和格式
    
    Args:
        products: 产品数据列表
        
    Returns:
        ProcessingResult: 验证结果和清理后的数据
    """
    pass

def prepare_processing_batches(products: List[ProductData], batch_size: int = 10) -> List[List[ProductData]]:
    """
    将产品数据分批处理
    
    Args:
        products: 产品数据列表
        batch_size: 每批处理的数量
        
    Returns:
        List[List[ProductData]]: 分批后的产品数据
    """
    pass
```

### 2.2 原子化拆解服务

```python
def decompose_product_names(products: List[ProductData]) -> ProcessingResult:
    """
    使用Google Gemini 2.5 Flash拆解产品名称为种子词
    
    Args:
        products: 产品数据列表
        
    Returns:
        ProcessingResult: 包含种子词列表或错误信息
    """
    pass

def extract_seed_words_single(product_name: str) -> ProcessingResult:
    """
    拆解单个产品名称
    
    Args:
        product_name: 产品名称
        
    Returns:
        ProcessingResult: 包含种子词列表或错误信息
    """
    pass

def save_decomposition_results(seed_words: List[SeedWord], file_path: str) -> ProcessingResult:
    """
    保存拆解结果到文件
    
    Args:
        seed_words: 种子词列表
        file_path: 保存文件路径
        
    Returns:
        ProcessingResult: 保存操作结果
    """
    pass

def load_cached_decomposition(cache_key: str) -> Optional[List[SeedWord]]:
    """
    加载缓存的拆解结果
    
    Args:
        cache_key: 缓存键
        
    Returns:
        Optional[List[SeedWord]]: 缓存的种子词列表或None
    """
    pass
```

### 2.3 API驱动扩展服务

```python
def expand_keywords_via_api(seed_words: List[str]) -> ProcessingResult:
    """
    使用Google Keywords Planner API扩展关键词
    
    Args:
        seed_words: 种子词列表
        
    Returns:
        ProcessingResult: 包含扩展关键词列表或错误信息
    """
    pass

def check_api_rate_limits() -> Dict[str, Union[int, bool]]:
    """
    检查API调用限制状态
    
    Returns:
        Dict: 包含调用次数、剩余次数、是否可调用等信息
    """
    pass

def call_keywords_planner_api(seed_word: str, max_keywords: int = 50) -> ProcessingResult:
    """
    调用Google Keywords Planner API获取关键词
    
    Args:
        seed_word: 种子词
        max_keywords: 最大返回关键词数量
        
    Returns:
        ProcessingResult: API响应结果
    """
    pass

def save_api_call_record(api_call: Dict) -> None:
    """
    记录API调用历史
    
    Args:
        api_call: API调用记录
    """
    pass

def get_cached_api_response(cache_key: str) -> Optional[List[ExpandedKeyword]]:
    """
    获取缓存的API响应
    
    Args:
        cache_key: 缓存键
        
    Returns:
        Optional[List[ExpandedKeyword]]: 缓存的关键词列表或None
    """
    pass
```

### 2.4 LLM驱动联想服务

```python
def associate_keywords_via_llm(keywords: List[str]) -> ProcessingResult:
    """
    使用Google Gemini进行关键词语义联想
    
    Args:
        keywords: 基础关键词列表
        
    Returns:
        ProcessingResult: 包含联想关键词列表或错误信息
    """
    pass

def generate_scenario_associations(keyword: str, scenario: str) -> ProcessingResult:
    """
    为特定场景生成关键词联想
    
    Args:
        keyword: 基础关键词
        scenario: 联想场景 (clinical_use, research_application, commercial_search)
        
    Returns:
        ProcessingResult: 场景化联想结果
    """
    pass

def score_keyword_relevance(base_keyword: str, associated_keyword: str) -> float:
    """
    评估关键词相关性得分
    
    Args:
        base_keyword: 基础关键词
        associated_keyword: 联想关键词
        
    Returns:
        float: 相关性得分 (0.0-1.0)
    """
    pass

def filter_association_quality(associations: List[AssociatedKeyword], min_score: float = 0.7) -> List[AssociatedKeyword]:
    """
    过滤低质量的联想关键词
    
    Args:
        associations: 联想关键词列表
        min_score: 最低相关性得分
        
    Returns:
        List[AssociatedKeyword]: 过滤后的高质量联想词
    """
    pass
```

### 2.5 结果管理服务

```python
def consolidate_results(
    decomposed: List[SeedWord],
    expanded: List[ExpandedKeyword], 
    associated: List[AssociatedKeyword]
) -> ProcessingResult:
    """
    整合所有阶段的关键词数据
    
    Args:
        decomposed: 拆解得到的种子词
        expanded: API扩展的关键词
        associated: LLM联想的关键词
        
    Returns:
        ProcessingResult: 整合后的最终数据
    """
    pass

def save_final_results(consolidated_data: Dict, output_path: str) -> ProcessingResult:
    """
    保存最终结果为CSV格式
    
    Args:
        consolidated_data: 整合后的数据
        output_path: 输出文件路径
        
    Returns:
        ProcessingResult: 保存操作结果
    """
    pass

def export_results_to_format(data: Dict, format: str, output_path: str) -> ProcessingResult:
    """
    导出结果为指定格式
    
    Args:
        data: 要导出的数据
        format: 导出格式 (csv, json, excel)
        output_path: 输出路径
        
    Returns:
        ProcessingResult: 导出操作结果
    """
    pass

def search_results_by_product(product_name: str, results_path: str) -> ProcessingResult:
    """
    按产品名称搜索历史结果
    
    Args:
        product_name: 产品名称
        results_path: 结果文件路径
        
    Returns:
        ProcessingResult: 搜索到的结果
    """
    pass
```

## 3. 配置管理接口

```python
def load_configuration(config_path: str = "deployment.config.toml") -> Dict:
    """
    加载系统配置
    
    Args:
        config_path: 配置文件路径
        
    Returns:
        Dict: 配置字典
    """
    pass

def get_api_configuration(api_name: str) -> Dict:
    """
    获取特定API的配置
    
    Args:
        api_name: API名称 (google_gemini, google_ads)
        
    Returns:
        Dict: API配置信息
    """
    pass

def validate_api_credentials() -> ProcessingResult:
    """
    验证API凭证的有效性
    
    Returns:
        ProcessingResult: 验证结果
    """
    pass
```

## 4. 缓存管理接口

```python
def initialize_cache_system() -> None:
    """初始化缓存系统"""
    pass

def get_cache_key(operation: str, params: Dict) -> str:
    """
    生成缓存键
    
    Args:
        operation: 操作类型
        params: 参数字典
        
    Returns:
        str: 缓存键
    """
    pass

def set_cache(key: str, value: any, ttl: int = 86400) -> None:
    """
    设置缓存
    
    Args:
        key: 缓存键
        value: 缓存值
        ttl: 生存时间（秒）
    """
    pass

def get_cache(key: str) -> Optional[any]:
    """
    获取缓存
    
    Args:
        key: 缓存键
        
    Returns:
        Optional[any]: 缓存值或None
    """
    pass

def clear_expired_cache() -> None:
    """清理过期缓存"""
    pass
```

## 5. 错误处理接口

```python
def handle_api_error(error: Exception, operation: str) -> ProcessingResult:
    """
    处理API调用错误
    
    Args:
        error: 异常对象
        operation: 操作名称
        
    Returns:
        ProcessingResult: 错误处理结果
    """
    pass

def log_error(error: Exception, context: Dict) -> None:
    """
    记录错误日志
    
    Args:
        error: 异常对象
        context: 错误上下文
    """
    pass

def retry_operation(operation: callable, max_retries: int = 3, delay: float = 1.0) -> ProcessingResult:
    """
    重试操作
    
    Args:
        operation: 要重试的操作
        max_retries: 最大重试次数
        delay: 重试延迟（秒）
        
    Returns:
        ProcessingResult: 操作结果
    """
    pass
```

## 6. 主控制器接口

```python
def run_full_pipeline(input_file: str, output_dir: str) -> ProcessingResult:
    """
    运行完整的关键词处理流水线
    
    Args:
        input_file: 输入CSV文件路径
        output_dir: 输出目录路径
        
    Returns:
        ProcessingResult: 完整流水线执行结果
    """
    pass

def run_single_stage(stage: str, input_data: any, config: Dict) -> ProcessingResult:
    """
    运行单个处理阶段

    Args:
        stage: 阶段名称 (decomposition, expansion, association)
        input_data: 输入数据
        config: 配置参数

    Returns:
        ProcessingResult: 阶段执行结果
    """
    pass
```

## 7. 调整后的核心接口（遵循编码要求）

### 7.1 AutoGen v0.4集成接口
```python
def initialize_autogen_decomposition_agent() -> AutoGenAgent:
    """
    初始化AutoGen分解代理（必须使用AutoGen v0.4）

    Returns:
        AutoGenAgent: 配置好的AutoGen代理
    """
    pass

def initialize_autogen_association_agent() -> AutoGenAgent:
    """
    初始化AutoGen联想代理（必须使用AutoGen v0.4）

    Returns:
        AutoGenAgent: 配置好的AutoGen代理
    """
    pass

def decompose_with_autogen(products: List[ProductData], agent: AutoGenAgent) -> List[SeedWord]:
    """
    使用AutoGen进行产品名称拆解

    Args:
        products: 产品数据列表
        agent: AutoGen代理实例

    Returns:
        List[SeedWord]: 拆解得到的种子词
    """
    pass

def associate_with_autogen(keywords: List[str], agent: AutoGenAgent) -> List[AssociatedKeyword]:
    """
    使用AutoGen进行关键词联想

    Args:
        keywords: 基础关键词列表
        agent: AutoGen代理实例

    Returns:
        List[AssociatedKeyword]: 联想关键词列表
    """
    pass
```

### 7.2 prompt_manager集成接口
```python
def load_prompts_from_manager(prompt_type: str) -> Dict[str, str]:
    """
    从prompt_manager系统加载提示词模板

    Args:
        prompt_type: 提示词类型 (decomposition, association, scenario)

    Returns:
        Dict[str, str]: 提示词模板字典
    """
    pass

def update_prompt_in_manager(prompt_name: str, prompt_content: str) -> bool:
    """
    更新prompt_manager中的提示词

    Args:
        prompt_name: 提示词名称
        prompt_content: 提示词内容

    Returns:
        bool: 更新是否成功
    """
    pass
```

### 7.3 API限制管理接口
```python
def check_api_quota_status() -> QuotaStatus:
    """
    检查Google Keywords Planner API配额状态

    Returns:
        QuotaStatus: 配额状态信息
    """
    pass

def record_api_call_with_protection(call_record: APICallRecord) -> bool:
    """
    记录API调用并检查限制

    Args:
        call_record: API调用记录

    Returns:
        bool: 是否允许调用
    """
    pass

def get_api_call_history(hours: int = 24) -> List[APICallRecord]:
    """
    获取API调用历史记录

    Args:
        hours: 查询的小时数

    Returns:
        List[APICallRecord]: 调用历史记录
    """
    pass
```

### 7.4 统一存储管理接口
```python
def save_to_unified_storage(data: any, data_type: str, filename: str) -> str:
    """
    保存数据到统一存储位置（results/目录）

    Args:
        data: 要保存的数据
        data_type: 数据类型 (csv, json, metadata)
        filename: 文件名

    Returns:
        str: 保存的文件路径
    """
    pass

def cleanup_temp_files() -> None:
    """
    即时清理无用的临时文件和测试文件
    """
    pass

def organize_results_directory() -> None:
    """
    组织results目录结构，确保易于寻找
    """
    pass
```
