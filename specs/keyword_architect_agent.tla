--------------------------- MODULE KeywordArchitectAgent ---------------------------
\* TLA+ Specification for Keyword Architect Agent
\* Critical Logic: API Rate Limiting and Cache Management
\* Version: 1.0
\* Date: 2025-07-17

EXTENDS Naturals, Sequences, FiniteSets

CONSTANTS
    MaxAPICallsPerHour,     \* Maximum API calls allowed per hour (10)
    CacheTTL,               \* Cache time-to-live in hours (24)
    MaxRetries,             \* Maximum retry attempts (3)
    BatchSize               \* Processing batch size (10)

VARIABLES
    apiCallCount,           \* Current API call count in current hour
    currentHour,            \* Current hour timestamp
    cache,                  \* Cache storage: [key |-> {value, timestamp}]
    apiCallHistory,         \* History of API calls: sequence of {timestamp, success}
    processingQueue,        \* Queue of items to process
    processingState,        \* Current processing state
    errorCount              \* Count of consecutive errors

\* Type definitions
APICall == [timestamp: Nat, success: BOOLEAN, retries: Nat]
CacheEntry == [value: STRING, timestamp: Nat, ttl: Nat]
ProcessingItem == [id: STRING, data: STRING, retries: Nat]

\* State space definition
TypeOK ==
    /\ apiCallCount \in 0..MaxAPICallsPerHour
    /\ currentHour \in Nat
    /\ cache \in [STRING -> CacheEntry]
    /\ apiCallHistory \in Seq(APICall)
    /\ processingQueue \in Seq(ProcessingItem)
    /\ processingState \in {"idle", "processing", "waiting", "error"}
    /\ errorCount \in 0..MaxRetries

\* Initial state
Init ==
    /\ apiCallCount = 0
    /\ currentHour = 0
    /\ cache = [key \in {} |-> [value |-> "", timestamp |-> 0, ttl |-> 0]]
    /\ apiCallHistory = <<>>
    /\ processingQueue = <<>>
    /\ processingState = "idle"
    /\ errorCount = 0

\* Helper predicates
CanMakeAPICall ==
    /\ apiCallCount < MaxAPICallsPerHour
    /\ processingState \in {"idle", "processing"}

CacheHit(key) ==
    /\ key \in DOMAIN cache
    /\ currentHour - cache[key].timestamp <= cache[key].ttl

CacheValid(key) ==
    /\ CacheHit(key)
    /\ cache[key].value # ""

\* API Rate Limiting Logic
CheckAPIRateLimit ==
    /\ processingState = "processing"
    /\ IF CanMakeAPICall
       THEN UNCHANGED <<apiCallCount, processingState>>
       ELSE /\ processingState' = "waiting"
            /\ UNCHANGED apiCallCount

\* Make API Call
MakeAPICall(key, success) ==
    /\ CanMakeAPICall
    /\ processingState = "processing"
    /\ apiCallCount' = apiCallCount + 1
    /\ apiCallHistory' = Append(apiCallHistory, 
                                [timestamp |-> currentHour, 
                                 success |-> success, 
                                 retries |-> 0])
    /\ IF success
       THEN /\ cache' = cache @@ (key :> [value |-> "api_result", 
                                          timestamp |-> currentHour, 
                                          ttl |-> CacheTTL])
            /\ errorCount' = 0
       ELSE /\ UNCHANGED cache
            /\ errorCount' = errorCount + 1
    /\ UNCHANGED <<currentHour, processingQueue, processingState>>

\* Cache Operations
GetFromCache(key) ==
    /\ CacheValid(key)
    /\ UNCHANGED <<apiCallCount, currentHour, cache, apiCallHistory, 
                   processingQueue, processingState, errorCount>>

SetCache(key, value) ==
    /\ cache' = cache @@ (key :> [value |-> value, 
                                  timestamp |-> currentHour, 
                                  ttl |-> CacheTTL])
    /\ UNCHANGED <<apiCallCount, currentHour, apiCallHistory, 
                   processingQueue, processingState, errorCount>>

\* Cache Cleanup
CleanupExpiredCache ==
    /\ cache' = [key \in DOMAIN cache |-> 
                 IF currentHour - cache[key].timestamp > cache[key].ttl
                 THEN [value |-> "", timestamp |-> 0, ttl |-> 0]
                 ELSE cache[key]]
    /\ UNCHANGED <<apiCallCount, currentHour, apiCallHistory, 
                   processingQueue, processingState, errorCount>>

\* Time Advancement
AdvanceTime ==
    /\ currentHour' = currentHour + 1
    /\ apiCallCount' = 0  \* Reset API call count each hour
    /\ CleanupExpiredCache
    /\ UNCHANGED <<apiCallHistory, processingQueue, processingState, errorCount>>

\* Processing Queue Management
AddToQueue(item) ==
    /\ processingQueue' = Append(processingQueue, item)
    /\ UNCHANGED <<apiCallCount, currentHour, cache, apiCallHistory, 
                   processingState, errorCount>>

ProcessNextItem ==
    /\ processingQueue # <<>>
    /\ processingState = "idle"
    /\ LET item == Head(processingQueue)
       IN /\ processingQueue' = Tail(processingQueue)
          /\ processingState' = "processing"
    /\ UNCHANGED <<apiCallCount, currentHour, cache, apiCallHistory, errorCount>>

\* Error Handling and Retry Logic
RetryFailedOperation ==
    /\ errorCount > 0
    /\ errorCount < MaxRetries
    /\ processingState = "error"
    /\ processingState' = "processing"
    /\ UNCHANGED <<apiCallCount, currentHour, cache, apiCallHistory, 
                   processingQueue, errorCount>>

AbortAfterMaxRetries ==
    /\ errorCount >= MaxRetries
    /\ processingState = "error"
    /\ processingState' = "idle"
    /\ errorCount' = 0
    /\ UNCHANGED <<apiCallCount, currentHour, cache, apiCallHistory, processingQueue>>

\* State Transitions
Next ==
    \/ CheckAPIRateLimit
    \/ \E key \in STRING, success \in BOOLEAN : MakeAPICall(key, success)
    \/ \E key \in STRING : GetFromCache(key)
    \/ \E key, value \in STRING : SetCache(key, value)
    \/ CleanupExpiredCache
    \/ AdvanceTime
    \/ \E item \in ProcessingItem : AddToQueue(item)
    \/ ProcessNextItem
    \/ RetryFailedOperation
    \/ AbortAfterMaxRetries

\* Specification
Spec == Init /\ [][Next]_<<apiCallCount, currentHour, cache, apiCallHistory, 
                           processingQueue, processingState, errorCount>>

\* Safety Properties
\* API rate limit is never exceeded
APIRateLimitSafety ==
    apiCallCount <= MaxAPICallsPerHour

\* Cache consistency: valid entries have non-empty values
CacheConsistency ==
    \A key \in DOMAIN cache : 
        (CacheHit(key) => cache[key].value # "")

\* Processing state consistency
ProcessingStateConsistency ==
    /\ (processingState = "processing" => processingQueue # <<>>)
    /\ (processingState = "waiting" => apiCallCount >= MaxAPICallsPerHour)
    /\ (processingState = "error" => errorCount > 0)

\* Error count bounds
ErrorCountBounds ==
    errorCount <= MaxRetries

\* Liveness Properties
\* Eventually all items in queue will be processed (assuming no permanent failures)
EventualProcessing ==
    (processingQueue # <<>>) ~> (processingQueue = <<>>)

\* API calls will eventually succeed if retried
EventualAPISuccess ==
    (errorCount > 0) ~> (errorCount = 0)

\* Cache will eventually be cleaned up
EventualCacheCleanup ==
    (\E key \in DOMAIN cache : 
        currentHour - cache[key].timestamp > cache[key].ttl) 
    ~> 
    (\A key \in DOMAIN cache : 
        currentHour - cache[key].timestamp <= cache[key].ttl)

\* Invariants
Invariants ==
    /\ TypeOK
    /\ APIRateLimitSafety
    /\ CacheConsistency
    /\ ProcessingStateConsistency
    /\ ErrorCountBounds

\* Temporal Properties
TemporalProperties ==
    /\ EventualProcessing
    /\ EventualAPISuccess
    /\ EventualCacheCleanup

=============================================================================

\* Model Configuration:
\* CONSTANTS:
\*   MaxAPICallsPerHour = 10
\*   CacheTTL = 24
\*   MaxRetries = 3
\*   BatchSize = 10
\*
\* SPECIFICATION: Spec
\* INVARIANTS: Invariants
\* PROPERTIES: TemporalProperties
\*
\* This model verifies:
\* 1. API rate limiting never allows more than MaxAPICallsPerHour calls
\* 2. Cache entries are consistent and properly managed
\* 3. Error handling and retry logic work correctly
\* 4. Processing queue eventually empties under normal conditions
\* 5. System recovers from transient failures
